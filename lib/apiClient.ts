import axios, { AxiosInstance, AxiosRequestConfig, AxiosError } from "axios";
import Cookies from "js-cookie";

// Types
interface RefreshResponse {
  success: boolean;
  error?: any;
}

// Backend API response structure
interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  errors?: any[];
}

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_BITFLUX_API_BASE_URL,
  timeout: 10000, // 10 seconds
});

// Singleton refresh token function
let refreshTokenInstance: (() => Promise<RefreshResponse>) | null = null;

export const setRefreshTokenInstance = (
  instance: () => Promise<RefreshResponse>
): void => {
  refreshTokenInstance = instance;
};

// Simple token utilities for API client
const TokenUtils = {
  getAccessToken: (): string | undefined => {
    return Cookies.get("accessToken");
  },

  getRefreshToken: (): string | undefined => {
    return Cookies.get("refreshToken");
  }
};

// Request interceptor
apiClient.interceptors.request.use(
  async (config: AxiosRequestConfig): Promise<AxiosRequestConfig> => {
    // If this is a token refresh request, don't try to refresh again
    if (config.url?.includes("/token")) {
      return config;
    }

    // Add the current token to the request
    const token = TokenUtils.getAccessToken();
    if (token) {
      config.headers = config.headers || {};
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error: AxiosError) => Promise.reject(error)
);

// Response interceptor
apiClient.interceptors.response.use(
  (response: any) => {
    // Handle backend's standardized response format
    if (response.data && typeof response.data === 'object') {
      // If response has success property, it's a standardized backend response
      if ('success' in response.data) {
        const apiResponse: ApiResponse = response.data;
        
        // If success is false, throw an error with proper message
        if (!apiResponse.success) {
          const error = new Error(apiResponse.error || apiResponse.message || 'API Error');
          (error as any).response = {
            ...response,
            data: apiResponse
          };
          throw error;
        }
        
        // For successful responses, return the data property if it exists
        // Otherwise return the whole response for backward compatibility
        if (apiResponse.data !== undefined) {
          return {
            ...response,
            data: apiResponse.data
          };
        }
      }
    }
    
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

    // Only attempt refresh on 401 errors, not for refresh token requests
    if (
      error.response?.status === 401 &&
      !originalRequest._retry &&
      refreshTokenInstance &&
      !originalRequest.url?.includes("/token")
    ) {
      originalRequest._retry = true;

      try {
        const { success } = await refreshTokenInstance();
        if (success) {
          const newToken = TokenUtils.getAccessToken();
          if (newToken) {
            originalRequest.headers = originalRequest.headers || {};
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            return apiClient(originalRequest);
          }
        }
      } catch (refreshError) {
        return Promise.reject(refreshError);
      }
    }

    // Handle backend error responses
    if (error.response?.data && typeof error.response.data === 'object') {
      const apiResponse: ApiResponse = error.response.data;
      if ('success' in apiResponse && !apiResponse.success) {
        const enhancedError = new Error(apiResponse.error || apiResponse.message || 'API Error');
        (enhancedError as any).response = error.response;
        (enhancedError as any).apiResponse = apiResponse;
        return Promise.reject(enhancedError);
      }
    }

    return Promise.reject(error);
  }
);

export default apiClient;
export type { ApiResponse };
