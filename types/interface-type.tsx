export interface RadAcctSession {
  radacctid: number;
  username: string;
  acctinputoctets: number;
  acctoutputoctets: number;
  acctstarttime: string;
  acctstoptime: string | null;
  acctsessiontime: number;
  acctinterval: number | null;
  callingstationid: string;
  acctterminatecause: string;
  framedipaddress: string;
}

export interface PackageInfo {
  id: number;
  package_name: string;
  price: string;
  download: string;
  upload: string;
  status: string;
  remote_group: string | null;
  vlan: number;
}

export interface GraphRange {
  timeLabel: string; // e.g., "17:50"
  timestamp: string;
  upload: Number;
  download: Number;
}

export interface CustomerInfo {
  id: number;
  username: string;
  password: string;
  first_name: string;
  last_name: string;
  email: string;
  register_date: string; // ISO date string
  org_id: string; // UUID

  contact: string[]; // Array of phone numbers
  address: string;
  valid_till: string; // ISO date string
  pack_id: number;

  country: number;
  province: number;
  city: number;
  municipality: number;
  ward: number;

  status: string;
}

// Backend user structure (from users table)
export interface User {
  id: number;
  username: string;
  email: string;
  password?: string;
  status: string;
  org_id: string;
  group_id: number;
  created_by?: number;
  created_at?: string;
  updated_at?: string;
}

// Backend organization structure
export interface Organization {
  id: string;
  name: string;
  parent_id?: string;
  created_at?: string;
  updated_at?: string;
}

// Backend group structure  
export interface Group {
  id: number;
  name: string;
  groupname: string;
  created_at?: string;
  updated_at?: string;
}

export interface NAS {
  id?: string;
  nasname: string;
  secret: string;
  description: string;
  vendor: string;
  name: string;
}

export interface NASFormData {
  id: number;
  nasname: string;
  secret: string;
  description: string;
  vendor: string;
}

export interface NASResponse {
  success: boolean;
  data: NAS[];
  message?: string;
}

export interface VoucherData {
  name: string;
  email: string;
  mobile: number;
  company: string;
  purpose: string;
  session?: string;
}

export interface Voucher {
  id: string;
  data: VoucherData;
  status: string;
  created_at: string;
  type: string;
  code: string;
}

export interface VoucherFormData {
  name: string;
  email: string;
  mobile: number;
  company: string;
  purpose: string;
  status: string;
  created_at: string;
  type: string;
  session: string;
}

// Backend guest grant structure (from user_grants table)
export interface UserGrant {
  id: string;
  status: string;
  type: string;
  data: any;
  created_at: string;
  updated_at?: string;
  code?: string;
}

// Backend standardized API response structure
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  errors?: any[];
}

// Backend refresh token structure
export interface RefreshToken {
  id: number;
  user_id: number;
  token: string;
  created_at: string;
  expires_at: string;
}

// Backend analytics structure
export interface Analytics {
  id: number;
  count: number;
  data: any[];
  created_at: string;
  inputMbps?: number;
  outputMbps?: number;
}

export interface DepartmentStats {
  username: string;
  nasipaddress: string;
  nasportid: string;
  nasporttype: string;
  acctstarttime: string;
  acctupdatetime: string;
  acctstoptime: string;
  acctinputoctets: string;
  acctoutputoctets: string;
  callingstationid: string;
  framedipaddress: string;
  acctterminatecause: string;
  groupname: string;
}