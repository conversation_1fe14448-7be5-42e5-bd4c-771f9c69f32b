"use client";

import type { ReactNode } from "react";
import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useRef,
} from "react";
import apiClient, { setRefreshTokenInstance } from "@/lib/apiClient";
import { useRefreshToken } from "@/hooks/useRefreshToken";
import Cookies from "js-cookie";
import jwt from "jsonwebtoken";

interface AuthContextType {
  isLoggedIn: boolean;
  isAuthReady: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  user?: {
    group?: string;
    actions?: string[];
  };
  org_id?: string;
  setOrg_Id: (org_id: string) => void;
  isRefreshed?: boolean;
  setIsRefreshed: (refreshed: boolean) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isAuthReady, setIsAuthReady] = useState(false);
  const [isRefreshed, setIsRefreshed] = useState(false);
  const [org_id, setOrg_Id] = useState("");
  const [user, setUser] = useState<AuthContextType["user"]>();
  const tokenManager = useRefreshToken();
  const isCheckingAuth = useRef(false);
  const initialAuthCheckDone = useRef(false);

  useEffect(() => {
    const access_token = Cookies.get("accessToken");
    const payload = jwt.decode(access_token);
    const org = payload?.org_id;
    setOrg_Id(org);
  }, []);

  // Register the refresh token function with the API client
  useEffect(() => {
    setRefreshTokenInstance(async () => {
      try {
        const success = await tokenManager.refreshToken();
        return { success };
      } catch (error: any) {
        if (error.response?.status === 403) {
          tokenManager.clearTokens();
        }
        return { success: false, error };
      }
    });
  }, [tokenManager]);

  // Check auth status on mount
  useEffect(() => {
    const checkAuth = async () => {
      if (isCheckingAuth.current) {
        return;
      }

      // Skip if we've already done the initial check
      if (initialAuthCheckDone.current) {
        return;
      }

      try {
        isCheckingAuth.current = true;
        setIsAuthReady(false);
        const { accessToken, refreshToken } = tokenManager.getTokens();

        // First check if current access token is valid
        if (accessToken) {
          setIsLoggedIn(true);
          initialAuthCheckDone.current = true;

          // Try to refresh in the background if we have a refresh token
          if (refreshToken) {
            try {
              await tokenManager.refreshToken();
            } catch (error: any) {
              console.error("Background token refresh error:", error);
            }
          }
          return;
        }

        if (refreshToken) {
          try {
            const success = await tokenManager.refreshToken();
            if (success) {
              setIsLoggedIn(true);
              initialAuthCheckDone.current = true;
              return;
            }
          } catch (error: any) {
            if (error.response?.status === 403) {
              tokenManager.clearTokens();
              setIsLoggedIn(false);
              return;
            }
          }
        }

        tokenManager.clearTokens();
        setIsLoggedIn(false);
      } catch (error: any) {
        const { accessToken } = tokenManager.getTokens();
        if (!accessToken) {
          tokenManager.clearTokens();
          setIsLoggedIn(false);
        } else {
          setIsLoggedIn(true);
        }
      } finally {
        setIsAuthReady(true);
        isCheckingAuth.current = false;
        initialAuthCheckDone.current = true;
      }
    };

    checkAuth();
  }, [tokenManager]);

  const login = async (username: string, password: string) => {
    try {
      const response = await apiClient.post("/login", { username, password });

      // Backend now returns: { success: true, message: "Login successful", data: { accessToken, refreshToken } }
      // The apiClient response interceptor extracts the data property
      if (!response?.data?.accessToken || !response?.data?.refreshToken) {
        throw new Error("Invalid response format from server");
      }

      tokenManager.setTokens(response?.data);

      // Set user info from JWT token
      const access_token = response?.data?.accessToken;
      const payload = jwt.decode(access_token) as any;
      if (payload) {
        setUser({
          group: payload.group_id,
          actions: payload.actions || []
        });
        setOrg_Id(payload.org_id);
      }

      setIsLoggedIn(true);
      initialAuthCheckDone.current = false;
    } catch (error: any) {
      tokenManager.clearTokens();
      setIsLoggedIn(false);
      // Enhanced error handling for backend standardized errors
      if (error.apiResponse) {
        throw new Error(error.apiResponse.error || error.apiResponse.message || "Login failed");
      }
      throw error;
    } finally {
      setIsAuthReady(true);
    }
  };

  const logout = () => {
    tokenManager.clearTokens();
    setUser(undefined);
    setIsLoggedIn(false);
    initialAuthCheckDone.current = false;
  };

  
  
  return (
    <AuthContext.Provider
      value={{ isRefreshed, setIsRefreshed, isLoggedIn, isAuthReady, login, logout, user, org_id, setOrg_Id }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
