# BitFlux Hotspot UI - Frontend Application

A modern, responsive frontend application for the BitFlux ISP management system, built with Next.js 15, React 19, and TypeScript.

## 🚀 Features

- **Modern Stack**: Next.js 15 with App Router, React 19, TypeScript
- **Authentication**: JWT-based authentication with automatic token refresh
- **Real-time Analytics**: Interactive dashboards with live data visualization
- **Customer Management**: Complete customer lifecycle management
- **User Management**: Role-based user administration
- **Guest Access**: Voucher and request-based guest WiFi management
- **Package Management**: Internet service package configuration
- **Device Management**: Network device (NAS) configuration
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Security**: Input validation, XSS protection, and secure token handling

## 🛠️ Technology Stack

### Core Technologies
- **Framework**: Next.js 15.2.4 with App Router
- **Language**: TypeScript 5.x
- **UI Library**: React 19
- **Styling**: Tailwind CSS 3.4.17
- **State Management**: React Context API
- **HTTP Client**: Axios with custom interceptors

### UI Components
- **Component Library**: Radix UI primitives
- **Charts**: Recharts for data visualization
- **Forms**: React Hook Form with Zod validation
- **Notifications**: Sonner for toast notifications
- **Icons**: Lucide React

### Development Tools
- **Build Tool**: Next.js built-in bundler
- **Type Checking**: TypeScript compiler
- **Linting**: ESLint with Next.js config
- **Package Manager**: npm/pnpm

## 📋 Prerequisites

- Node.js 18.x or later
- npm 8.x or later (or pnpm 8.x)
- Backend API server running (BitFlux backend)

## 🔧 Installation

1. **Clone the repository**
   ```bash
   git clone https://gitlab.workalaya.com/bitflux/hotspot/hotspot-ui.git
   cd hotspot-ui
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   pnpm install
   ```

3. **Environment Configuration**
   
   Create a `.env.local` file in the root directory:
   ```env
   NEXT_PUBLIC_BITFLUX_API_BASE_URL=http://localhost:3001
   ```

4. **Start the development server**
   ```bash
   npm run dev
   # or
   pnpm dev
   ```

5. **Access the application**
   Open [http://localhost:3000](http://localhost:3000) in your browser

## 🏗️ Project Structure

```
hotspot-ui/
├── app/                    # Next.js 15 App Router pages
│   ├── (auth)/            # Authentication pages
│   ├── app/               # Main application pages
│   │   ├── analytics/     # Analytics dashboard
│   │   ├── department/    # Department management
│   │   ├── guest/         # Guest access management
│   │   ├── settings/      # System settings
│   │   │   ├── devices/   # Device management
│   │   │   ├── groups/    # Group management
│   │   │   └── users/     # User management
│   │   ├── staff/         # Staff management
│   │   └── userprofile/   # User profile
│   ├── globals.css        # Global styles
│   └── root-layout.tsx    # Root layout component
├── components/            # React components
│   ├── analytics/         # Analytics components
│   ├── Auth/             # Authentication components
│   ├── customer/         # Customer management
│   ├── department/       # Department components
│   ├── guest/            # Guest access components
│   ├── settings/         # Settings components
│   └── ui/               # Reusable UI components
├── context/              # React Context providers
├── hooks/                # Custom React hooks
├── lib/                  # Utility libraries
├── types/                # TypeScript type definitions
├── public/               # Static assets
└── styles/               # Additional styles
```

## 🔑 Authentication Flow

The application uses JWT-based authentication with automatic token refresh:

1. **Login**: User credentials are validated against the backend
2. **Token Storage**: Access and refresh tokens are stored in secure cookies
3. **API Requests**: All API calls include the access token in headers
4. **Token Refresh**: Automatic token refresh on expiration
5. **Logout**: Tokens are cleared from cookies and local storage

### Authentication Components
- `AuthContext`: Global authentication state management
- `AuthGuard`: Protected route wrapper
- `useRefreshToken`: Token refresh hook
- `apiClient`: HTTP client with token interceptors

## 🌐 API Integration

The frontend integrates with the BitFlux backend API using a standardized response format:

### Response Format
```typescript
interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  errors?: any[];
}
```

### API Endpoints
- **Authentication**: `/login`, `/token`
- **Users**: `/user`, `/user/{id}`
- **Customers**: `/customer`, `/customer/{id}`
- **Packages**: `/package`, `/package/{id}`
- **Groups**: `/groups`, `/group/{name}`
- **Devices**: `/nas`, `/nas/{id}`
- **Analytics**: `/analytics`, `/department/stats`
- **Guest Access**: `/guest/voucher/`, `/guest/grant`

### Error Handling
- Automatic retry for failed requests
- User-friendly error messages
- Loading states and error boundaries
- Network error recovery

## 📊 Key Features

### Dashboard
- Real-time client connection monitoring
- Bandwidth usage analytics
- Interactive charts and graphs
- Time-range selection (1hr, 6hr, 12hr, 24hr, 5 days)

### Customer Management
- Customer profile management
- Package assignment and billing
- Usage statistics and analytics
- Account status monitoring

### User Management
- Role-based access control
- User profile management
- Group assignments
- Permission management

### Guest Access
- Voucher-based access system
- Sponsored guest requests
- Time-limited access controls
- Access code generation

### Package Management
- Internet service package configuration
- Bandwidth allocation
- VLAN assignment
- Pricing management

## 🔐 Security Features

### Authentication Security
- JWT token validation
- Secure cookie storage
- Automatic token refresh
- Session timeout handling

### Input Validation
- Form validation with Zod schemas
- XSS protection
- SQL injection prevention
- Input sanitization

### API Security
- Request/response interceptors
- Rate limiting awareness
- Error message sanitization
- Secure headers handling

## 🎨 UI/UX Features

### Design System
- Consistent color palette
- Typography system
- Spacing and layout guidelines
- Component library

### Responsive Design
- Mobile-first approach
- Flexible grid system
- Touch-friendly interactions
- Adaptive layouts

### Accessibility
- Keyboard navigation
- Screen reader support
- High contrast mode
- Focus management

## 📱 Development Workflow

### Available Scripts
```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production (skips linting)
npm run start        # Start production server
npm run lint         # Run ESLint

# Production
npm run build        # Build optimized production bundle
npm run start        # Start production server
```

### Development Guidelines
- Use TypeScript for all new code
- Follow ESLint configuration
- Implement proper error boundaries
- Write meaningful component tests
- Use semantic HTML elements
- Follow accessibility guidelines

## 🔄 State Management

### Context Providers
- `AuthContext`: Authentication state
- `ThemeProvider`: Theme configuration

### Custom Hooks
- `useAuth`: Authentication utilities
- `useRefreshToken`: Token management
- `useFetchOnMount`: Generic data fetching

## 📈 Performance Optimizations

### Build Optimizations
- Tree shaking for smaller bundles
- Code splitting by route
- Image optimization
- Static asset optimization

### Runtime Optimizations
- Lazy loading for routes
- Component memoization
- Efficient re-renders
- Optimal bundle sizes

## 🐛 Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Check API base URL configuration
   - Verify backend server is running
   - Clear browser cookies and try again

2. **Build Errors**
   - Update dependencies: `npm update`
   - Clear build cache: `rm -rf .next`
   - Rebuild: `npm run build`

3. **API Connection Issues**
   - Verify backend API is accessible
   - Check network connectivity
   - Validate environment variables

### Debug Mode
Enable debug logging by setting:
```env
NODE_ENV=development
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

### Code Style
- Use TypeScript for type safety
- Follow the existing code style
- Add proper JSDoc comments
- Use meaningful variable names
- Implement proper error handling

## 📄 License

This project is proprietary software. All rights reserved.

## 🔗 Related Projects

- [BitFlux Backend](../bitflux-hotspot) - Backend API server
- [BitFlux Documentation](../bitflux-hotspot/docs) - Comprehensive system documentation

## 📞 Support

For technical support and questions:
- Review the documentation
- Check the troubleshooting guide
- Contact the development team

---

**Built with ❤️ by the BitFlux team**