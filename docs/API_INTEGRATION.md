# API Integration Documentation

This document provides comprehensive guidance for integrating with the BitFlux backend API from the frontend application.

## Table of Contents

1. [Overview](#overview)
2. [Authentication](#authentication)
3. [API Response Format](#api-response-format)
4. [Error Handling](#error-handling)
5. [API Endpoints](#api-endpoints)
6. [Integration Examples](#integration-examples)
7. [Best Practices](#best-practices)
8. [Troubleshooting](#troubleshooting)

## Overview

The BitFlux frontend integrates with a Node.js/Express backend API that provides:
- JWT-based authentication with refresh tokens
- Standardized response format across all endpoints
- ABAC (Attribute-Based Access Control) authorization
- Comprehensive error handling
- Real-time data updates

### Base Configuration

```typescript
// Environment variable
NEXT_PUBLIC_BITFLUX_API_BASE_URL=http://localhost:3001

// API Client setup
const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_BITFLUX_API_BASE_URL,
  timeout: 10000,
});
```

## Authentication

### JWT Token System

The backend uses JWT tokens with the following structure:

```typescript
interface TokenPayload {
  user_id: number;
  org_id: string;
  group_id: number;
  username: string;
  exp: number;
  iat: number;
}
```

### Authentication Flow

1. **Login Request**
   ```typescript
   POST /login
   Content-Type: application/json
   
   {
     "username": "<EMAIL>",
     "password": "password123"
   }
   ```

2. **Login Response**
   ```typescript
   {
     "success": true,
     "message": "Login successful",
     "data": {
       "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
       "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
     }
   }
   ```

3. **Token Refresh**
   ```typescript
   POST /token
   Content-Type: application/json
   
   {
     "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
   }
   ```

4. **Token Refresh Response**
   ```typescript
   {
     "success": true,
     "message": "Token refreshed successfully",
     "data": {
       "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
     }
   }
   ```

### Token Storage

Tokens are stored in secure HTTP-only cookies:

```typescript
// Access token (30 minutes in production, 30 days in dev)
Cookies.set("accessToken", token, {
  expires: new Date(Date.now() + 30 * 60 * 1000),
  path: "/",
  sameSite: "Strict",
  secure: window.location.protocol === "https:"
});

// Refresh token (7 days in production, 100 days in dev)
Cookies.set("refreshToken", token, {
  expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
  path: "/",
  sameSite: "Strict",
  secure: window.location.protocol === "https:"
});
```

## API Response Format

All API responses follow a standardized format:

```typescript
interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  errors?: any[];
}
```

### Success Response Example

```typescript
{
  "success": true,
  "message": "List Customer",
  "data": [
    {
      "id": 1,
      "username": "john_doe",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "status": "active"
    }
  ]
}
```

### Error Response Example

```typescript
{
  "success": false,
  "message": "Validation failed",
  "error": "Username already exists",
  "errors": [
    {
      "field": "username",
      "message": "Username must be unique"
    }
  ]
}
```

## Error Handling

### Frontend Error Handling Pattern

```typescript
try {
  const response = await apiClient.get('/user');
  // The apiClient response interceptor extracts the data property
  setUsers(response.data || []);
} catch (error) {
  console.error('API Error:', error);
  // error.message contains user-friendly error message
  toast.error(error.message || 'An error occurred');
}
```

### Common Error Codes

| Status Code | Description | Handling |
|-------------|-------------|----------|
| 400 | Bad Request | Show validation errors |
| 401 | Unauthorized | Redirect to login |
| 403 | Forbidden | Show access denied message |
| 404 | Not Found | Show "not found" message |
| 409 | Conflict | Show conflict resolution options |
| 500 | Internal Server Error | Show generic error message |

## API Endpoints

### Authentication Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/login` | User authentication | No |
| POST | `/token` | Refresh access token | No |

### User Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/user` | Get all users (paginated) | Yes |
| GET | `/user?id={id}` | Get user by ID | Yes |
| POST | `/user` | Create new user | Yes |
| PATCH | `/user/{id}` | Update user | Yes |
| DELETE | `/user/{id}` | Delete user | Yes |

### Customer Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/customer` | Get all customers | Yes |
| GET | `/customer/{id}` | Get customer by ID | Yes |
| GET | `/customer/stats/{username}` | Get customer stats | Yes |
| POST | `/customer` | Create new customer | Yes |
| PATCH | `/customer/{id}` | Update customer | Yes |
| DELETE | `/customer/{id}` | Delete customer | Yes |

### Package Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/package` | Get all packages | Yes |
| POST | `/package` | Create new package | Yes |
| PATCH | `/package/{id}` | Update package | Yes |
| DELETE | `/package/{id}` | Delete package | Yes |

### Group Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/groups` | Get all groups | Yes |
| GET | `/group/{groupname}` | Get group by name | Yes |
| POST | `/group` | Create new group | Yes |
| PATCH | `/group/{groupname}` | Update group | Yes |
| DELETE | `/group/{groupname}` | Delete group | Yes |

### Device Management (NAS)

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/nas` | Get all NAS devices | Yes |
| POST | `/nas` | Create new NAS device | Yes |
| PATCH | `/nas/{id}` | Update NAS device | Yes |
| DELETE | `/nas/{id}` | Delete NAS device | Yes |

### Analytics

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/analytics` | Get analytics data | Yes |
| POST | `/department/stats` | Get department statistics | Yes |

### Guest Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/guest/voucher/?type=guest` | Get guest vouchers | Yes |
| GET | `/guest/voucher/?type=voucher` | Get voucher data | Yes |
| POST | `/guest/voucher` | Create voucher | Yes |
| POST | `/guest/grant` | Grant guest access | No |
| POST | `/guest/request` | Request guest access | No |

## Integration Examples

### Basic API Call

```typescript
// GET request with authentication
const fetchUsers = async () => {
  try {
    const response = await apiClient.get('/user');
    // Response data is automatically extracted by interceptor
    setUsers(response.data || []);
  } catch (error) {
    console.error('Failed to fetch users:', error);
    toast.error(error.message || 'Failed to fetch users');
  }
};
```

### POST Request with Data

```typescript
// POST request with form data
const createUser = async (userData) => {
  try {
    const response = await apiClient.post('/user', userData);
    // Backend returns: { success: true, message: "User has been created" }
    toast.success('User created successfully');
    return response.data;
  } catch (error) {
    console.error('Failed to create user:', error);
    toast.error(error.message || 'Failed to create user');
    throw error;
  }
};
```

### PATCH Request with Updates

```typescript
// PATCH request for updates
const updateUser = async (id, updates) => {
  try {
    const response = await apiClient.patch(`/user/${id}`, updates);
    // Backend returns: { message: "updated successfully" }
    toast.success('User updated successfully');
    return response.data;
  } catch (error) {
    console.error('Failed to update user:', error);
    toast.error(error.message || 'Failed to update user');
    throw error;
  }
};
```

### DELETE Request

```typescript
// DELETE request
const deleteUser = async (id) => {
  try {
    await apiClient.delete(`/user/${id}`);
    // Backend returns: { message: "User has been deleted" }
    toast.success('User deleted successfully');
  } catch (error) {
    console.error('Failed to delete user:', error);
    toast.error(error.message || 'Failed to delete user');
    throw error;
  }
};
```

### Analytics Request

```typescript
// POST request for analytics
const fetchAnalytics = async (timeRange) => {
  try {
    const response = await apiClient.post('/analytics', {
      created_from: timeRange.start,
      created_to: timeRange.end
    });
    // Backend returns: { success: true, message: "Analytics", data: [...] }
    return response.data || [];
  } catch (error) {
    console.error('Failed to fetch analytics:', error);
    toast.error(error.message || 'Failed to fetch analytics');
    throw error;
  }
};
```

## Best Practices

### 1. Error Handling

```typescript
// Always handle errors appropriately
try {
  const response = await apiClient.get('/endpoint');
  // Process successful response
} catch (error) {
  // Log for debugging
  console.error('API Error:', error);
  
  // Show user-friendly message
  toast.error(error.message || 'An error occurred');
  
  // Handle specific error cases
  if (error.response?.status === 401) {
    // Handle authentication error
    redirectToLogin();
  }
}
```

### 2. Loading States

```typescript
const [loading, setLoading] = useState(false);

const fetchData = async () => {
  setLoading(true);
  try {
    const response = await apiClient.get('/endpoint');
    setData(response.data);
  } catch (error) {
    handleError(error);
  } finally {
    setLoading(false);
  }
};
```

### 3. Type Safety

```typescript
// Define response types
interface User {
  id: number;
  username: string;
  email: string;
  status: string;
}

// Use typed responses
const fetchUsers = async (): Promise<User[]> => {
  const response = await apiClient.get('/user');
  return response.data as User[];
};
```

### 4. Reusable API Hooks

```typescript
// Custom hook for API calls
const useApiCall = <T>(endpoint: string) => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await apiClient.get(endpoint);
      setData(response.data);
    } catch (err) {
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return { data, loading, error, fetchData };
};
```

## Troubleshooting

### Common Issues and Solutions

1. **Token Expiration**
   - **Issue**: API calls fail with 401 status
   - **Solution**: Token refresh is handled automatically by the API client

2. **Network Errors**
   - **Issue**: Request timeout or connection refused
   - **Solution**: Check backend server status and network connectivity

3. **CORS Issues**
   - **Issue**: Cross-origin requests blocked
   - **Solution**: Ensure backend CORS is configured correctly

4. **Response Format Errors**
   - **Issue**: Unexpected response structure
   - **Solution**: Verify backend API version and response format

### Debug Mode

Enable debug logging:

```typescript
// Add request/response logging
apiClient.interceptors.request.use(request => {
  console.log('Starting Request:', request);
  return request;
});

apiClient.interceptors.response.use(
  response => {
    console.log('Response:', response);
    return response;
  },
  error => {
    console.error('Response Error:', error);
    return Promise.reject(error);
  }
);
```

### Testing API Integration

```typescript
// Test API endpoints
describe('API Integration', () => {
  it('should fetch users successfully', async () => {
    const response = await apiClient.get('/user');
    expect(response.data).toBeDefined();
    expect(Array.isArray(response.data)).toBe(true);
  });

  it('should handle authentication errors', async () => {
    // Remove auth token
    apiClient.defaults.headers.common['Authorization'] = '';
    
    try {
      await apiClient.get('/user');
    } catch (error) {
      expect(error.response.status).toBe(401);
    }
  });
});
```

## Conclusion

This documentation provides a comprehensive guide for integrating with the BitFlux backend API. Follow the patterns and best practices outlined here to ensure robust, maintainable, and secure API integration in your frontend application.

For additional support, refer to the backend API documentation and the main project README.