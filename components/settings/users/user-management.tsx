// components/settings/users/user-management.tsx
"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { SquarePen } from "@/components/icons/list"; // Assuming this path is correct
import { DeleteUserDialog } from "@/app/app/settings/users/page";

export type UserGroup = string;
export type UserOrganization = string;

export interface User {
  id: string;
  username: string;
  email: string;
  group: UserGroup;
  password: string;
  status: string;
  organization: UserOrganization;
}

// User Table Component
export function UserTable({
  users,
  onEdit,
  onDelete,
}: {
  users: User[];
  onDelete: (id: string) => void;
  onEdit: (user: User) => void;
}) {
  // const router = useRouter();
  return (
    // Added overflow-x-auto for horizontal scrolling on small screens
    <div className="bg-white rounded-lg shadow p-4 overflow-x-auto">
      <div className="w-full overflow-x-auto ">
        <table className="min-w-max w-full">
          <thead className="bg-gray-200 text-left text-xs uppercase align-middle">
            <tr>
              <th className="px-4 py-2 ">S.N.</th>
              <th className="px-4 py-2 ">Username</th>
              <th className="px-4 py-2 ">Email</th>
              <th className="px-4 py-2 ">Status</th>
              <th className="px-4 py-2 ">Group</th>
              <th className="px-4 py-2 text-end">Actions</th>
            </tr>
          </thead>
          <tbody>
            {users?.length === 0 ? (
              <tr>
                <td
                  colSpan={7}
                  className="px-1 sm:px-2 py-8 text-center text-blue-900 text-sm"
                >
                  Oops! No user matched your search
                </td>
              </tr>
            ) : (
              users?.map((user, index) => (
                <tr
                  key={user?.id}
                  id={user?.id}
                  className="border-b hover:bg-gray-50"
                >
                  <td className="px-4 py-1 text-xs">{index + 1}</td>
                  <td className="px-4 py-1 text-xs">{user?.username}</td>
                  <td className="px-4 py-1 text-xs">{user?.email}</td>
                  <td className="px-4 py-1 text-xs">
                    <StatusBadge status={user?.status} />
                  </td>
                  <td className="px-4 py-1 text-xs">
                    <GroupBadge group={user?.group} />
                  </td>
                  <td className="px-4 py-1 text-xs">
                    <div className="flex items-center justify-end gap-1">
                      <Button
                        onClick={() => {
                          // sessionStorage.setItem("editUser", JSON.stringify(user));
                          // router.push(`/app/settings/users/edit/${user.id}`);
                          onEdit?.(user);
                        }}
                        className="bg-green-500 hover:bg-green-600 text-white p-1 rounded-md h-7 w-7"
                      >
                        <SquarePen className="h-4 w-4" />
                      </Button>
                      <DeleteUserDialog
                        username={user?.username}
                        onDelete={() => onDelete?.(user?.id)}
                      />
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
// Add User Form Component
export function AddUserForm({ onSubmit }: { onSubmit: (user: User) => void }) {
  const [newUser, setNewUser] = useState({
    username: "",
    email: "", // Added email to initial state for consistency with User type
    password: "",
    confirmPassword: "",
    group: "user" as UserGroup,
    status: "active", // Added default status
    organization: "", // Added default organization
  });

  const [passwordStrength, setPasswordStrength] = useState({
    length: false,
    uppercase: false,
    number: false,
  });

  const handlePasswordChange = (password: string) => {
    setPasswordStrength({
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      number: /\d/.test(password),
    });
    setNewUser((prev) => ({ ...prev, password }));
  };

  const isValid =
    newUser?.username.trim() !== "" &&
    newUser?.email.trim() !== "" && // Added email validation
    passwordStrength.length &&
    passwordStrength.uppercase &&
    passwordStrength.number &&
    newUser?.password === newUser?.confirmPassword;

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit?.({
          id: Date.now().toString(), // Ensure id is string
          ...newUser,
          username: newUser?.username.trim(),
          email: newUser?.email.trim(), // Trim email
          // Password, status, organization, and group are already in newUser
        });
        setNewUser({
          username: "",
          email: "",
          password: "",
          confirmPassword: "",
          group: "user",
          status: "active",
          organization: "",
        });
      }}
      className="space-y-4"
    >
      <Input
        placeholder="Username"
        value={newUser?.username}
        onChange={(e) =>
          setNewUser((prev) => ({ ...prev, username: e.target.value }))
        }
      />
      <Input // Added email input
        type="email"
        placeholder="Email"
        value={newUser?.email}
        onChange={(e) =>
          setNewUser((prev) => ({ ...prev, email: e.target.value }))
        }
      />

      <PasswordInput
        value={newUser?.password}
        onChange={handlePasswordChange}
        strength={passwordStrength}
      />

      <Input
        type="password"
        placeholder="Confirm Password"
        value={newUser?.confirmPassword}
        onChange={(e) =>
          setNewUser((prev) => ({ ...prev, confirmPassword: e.target.value }))
        }
      />
      <select
        value={newUser?.group}
        onChange={(e) =>
          setNewUser((prev) => ({
            ...prev,
            group: e.target.value as UserGroup,
          }))
        }
        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-60"
      >
        <option value="user">User</option>
        <option value="admin">Admin</option>
        {/* Add more group options as needed */}
      </select>

      <Button type="submit" className="w-full" disabled={!isValid}>
        Create User
      </Button>
    </form>
  );
}
// Shared Components
function GroupBadge({ group }: { group: UserGroup }) {
  // Generate a consistent color based on the group name
  const getGroupColor = (groupName: string) => {
    const colors = [
      "bg-blue-100 text-blue-800",
      "bg-purple-100 text-purple-800",
      "bg-red-100 text-red-800",
      "bg-green-100 text-green-800",
      "bg-yellow-100 text-yellow-800",
      "bg-indigo-100 text-indigo-800",
      "bg-pink-100 text-pink-800",
      "bg-gray-100 text-gray-800",
    ];

    // Simple hash function to get consistent color for each group
    let hash = 0;
    for (let i = 0; i < groupName.length; i++) {
      hash = ((hash << 5) - hash + groupName.charCodeAt(i)) & 0xffffffff;
    }
    return colors[Math.abs(hash) % colors.length];
  };

  return (
    <span className={`px-2 py-1 rounded-md text-xs ${getGroupColor(group)}`}>
      {group}
    </span>
  );
}

function StatusBadge({ status }: { status: string }) {
  const statusStyles: { [key: string]: string } = {
    // Explicitly type statusStyles
    active: "bg-green-300 text-green-800",
    inactive: "bg-red-100 text-red-800",
  };

  return (
    <span
      className={`px-2 py-1 rounded-md text-xs ${
        statusStyles[status] || "bg-gray-100 text-gray-800"
      }`}
    >
      {/* Added fallback style */}
      {status}
    </span>
  );
}

function PasswordInput({
  value,
  onChange,
  strength,
  placeholder = "Password",
}: {
  value: string;
  onChange: (password: string) => void;
  strength: { length: boolean; uppercase: boolean; number: boolean };
  placeholder?: string;
}) {
  return (
    <div className="space-y-2">
      <Input
        type="password"
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
      />
      <div className="flex flex-wrap gap-2 text-sm">
        {/* Added flex-wrap */}
        <span className={strength.length ? "text-green-600" : "text-gray-500"}>
          • 8+ characters
        </span>
        <span
          className={strength.uppercase ? "text-green-600" : "text-gray-500"}
        >
          • Uppercase
        </span>
        <span className={strength.number ? "text-green-600" : "text-gray-500"}>
          • Number
        </span>
      </div>
    </div>
  );
}
