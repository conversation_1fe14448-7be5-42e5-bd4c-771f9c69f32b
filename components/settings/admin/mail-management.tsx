"use client";

import { useEffect, useState, React } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Eye, EyeOff } from "@/components/icons/list";
// import { useFetch } from "@/hooks/useFetchOnMount";
import { toast } from "sonner";
import apiClient from "@/lib/apiClient";

export default function ManageMail({
  initialData,
  onSubmit,
  onClose,
}: {
  initialData: any;
  onSubmit: (mail: any) => void;
  onClose: () => void;
}) {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    host: "",
    port: "",
    secure: false,
  });

  const [isEditMode, setIsEditMode] = useState(false);
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showConfirmPasswordText, setShowConfirmPasswordText] = useState(false);
  const [isValid, setIsValid] = useState(false);
  const [passwordMismatch, setPasswordMismatch] = useState(false);
const [loading,setLoadingTrue] = useState(true)
//   const { data: fetchedData, loading, error } = useFetch("/admin?func=email");
//   const [initialData, setInititalData] = useState();
  // console.error(fetchedData)
  useEffect(() => {
    if (initialData) {
    //   setInititalData(fetchedData[0]);
      const emailData = initialData
      setFormData({
        email: emailData.email || "",
        password: "",
        host: emailData.host || "",
        port: emailData.port || "",
        secure: emailData.secure ,
      });
      setIsEditMode(true);
    }
      setLoadingTrue(false)
  }, [initialData]);

  const getChangedFields = () => {
    const diff: Record<string, any> = {};
    for (const key in formData) {
      if (key === "confirmPassword") continue;
      if (formData[key] !== initialData[key]) {
          if (key === "password" && formData[key] === "") continue;
          if(key === "secure"){diff[key]=String(formData[key])}
        diff[key] = formData[key];
      }
    }
    return diff;
  };
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    if (name === "password") setShowConfirmPassword(true);
  };

  const handleChangePort = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    if (/^\d*$/.test(value)) {
      setFormData((prev) => ({ ...prev, port: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (passwordMismatch) {
      toast.error("Passwords do not match");
      return;
    }

    const payload = {
      key: "email",
      value: {
        email: formData.email.trim(),
        password: formData.password || undefined,
        host: formData.host.trim(),
        port: formData.port,
        secure: String(formData.secure),
      },
    };

    try {
      if (!isEditMode) {
        const res = await apiClient.post("/admin/email", payload);
        const json = await res.json();

        if (res.ok && json.success) {
          onSubmit?.(formData);
          toast.success(json.message || "Email config added successfully");
        } else {
          toast.error(json.message || "Error saving email config");
        }
      } else {
          const changedFields = getChangedFields();
          const emailPayload = {key:"email",value:changedFields}
        const res = await apiClient.patch("/admin/email", emailPayload);
        const json = await res.json();

        if (res.ok && json.success) {
        //   onSubmit?.(formData);
            toast.success(json.message || "Email config updated successfully");
            onClose();
        } else {
          toast.error(json.message || "Error updating email config");
        }
      }
    } catch (err) {
      console.error(err);
      toast.error("Internal server error");
    }
  };

  useEffect(() => {
    const emailValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email.trim());
    const hostValid = formData.host.trim() !== "";
    const portValid = /^\d+$/.test(formData.port) && Number(formData.port) > 0;
    const passwordValid =
      !showConfirmPassword ||
      (formData.password === confirmPassword &&
        (!formData.password ||
          /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^A-Za-z\d]).{8,}$/.test(
            formData.password
          )));

    setIsValid(emailValid && hostValid && portValid && passwordValid);
    setPasswordMismatch(
      showConfirmPassword && formData.password !== confirmPassword
    );
  }, [formData, confirmPassword, showConfirmPassword]);

  if (loading)
    return <div className="text-center py-10">Loading email config...</div>;
//   if (error) {
//     toast.error("Failed to fetch email config");
//     return null;
//   }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm" />
      <div className="relative bg-white rounded-xl shadow-2xl overflow-hidden max-w-md md:max-w-lg w-full max-h-[90vh] flex flex-col">
        <div className="px-5 py-3 bg-gradient-to-r bg-primary text-white border-b">
          <h1 className="text-lg font-extrabold text-center">
            {isEditMode ? "Update Mail" : "Add Mail"}
          </h1>
          <p className="text-blue-100 text-sm text-center">
            {isEditMode
              ? "Update existing email configuration"
              : "Add email configuration to enable mailing"}
          </p>
        </div>

        <form
          onSubmit={handleSubmit}
          className="flex-grow p-10 grid grid-cols-1 gap-x-6 gap-y-5 overflow-y-auto custom-scrollbar"
        >
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Email</label>
            <Input
              value={formData.email}
              name="email"
              onChange={handleChange}
              placeholder="Email Address"
              required
              pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
            />
            {formData.email &&
              !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email.trim()) && (
                <p className="text-xs text-red-500">
                  Please enter a valid email address
                </p>
              )}
          </div>

          <div className="relative space-y-2">
            <label className="text-xs font-semibold text-gray-700">
              Password
            </label>
            <div className="relative">
              <Input
                type={showPassword ? "text" : "password"}
                name="password"
                value={formData.password}
                onChange={handleChange}
                placeholder={
                  isEditMode
                    ? "New Password (leave blank to keep current)"
                    : "Password"
                }
                className="pr-12"
                minLength={8}
                required={!isEditMode}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-2 top-1/2 -translate-y-1/2 h-8 px-2 py-1 text-gray-500 hover:bg-gray-200 rounded-md"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          {(showConfirmPassword || !isEditMode) && (
            <div className="space-y-2">
              <label className="text-xs font-medium text-gray-700">
                Confirm Password
              </label>
              <div className="relative">
                <Input
                  type={showConfirmPasswordText ? "text" : "password"}
                  placeholder="Confirm Password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className={passwordMismatch ? "border-red-500" : ""}
                  required={!isEditMode || !!formData.password}
                  minLength={8}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/4 -translate-y-1 h-8"
                  onClick={() =>
                    setShowConfirmPasswordText(!showConfirmPasswordText)
                  }
                >
                  {showConfirmPasswordText ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {passwordMismatch && (
                <p className="text-xs text-red-500">Passwords do not match.</p>
              )}
              {formData.password &&
                !/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^A-Za-z\d]).{8,}$/.test(
                  formData.password
                ) && (
                  <p className="text-xs text-red-500">
                    Password must contain at least 8 characters with uppercase,
                    lowercase, number, and special character.
                  </p>
                )}
            </div>
          )}

          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              SMTP Host
            </label>
            <Input
              value={formData.host}
              name="host"
              onChange={handleChange}
              placeholder="SMTP Host"
              required
            />
          </div>

          <div className="inline-flex items-center space-x-9">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Port</label>
              <Input
                type="number"
                name="port"
                min="1"
                value={formData.port}
                onChange={handleChangePort}
                placeholder="Enter Port"
                className="appearance-none"
                required
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Secure
              </label>
              <div className="flex items-center">
                <button
                  type="button"
                  onClick={() =>
                    setFormData((prev) => ({ ...prev, secure: !prev.secure }))
                  }
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none ${
                    formData.secure ? "bg-green-600" : "bg-gray-400"
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      formData.secure ? "translate-x-6" : "translate-x-1"
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>

          <div className="mt-3 space-y-2">
            <Button
              type="submit"
              className="w-full bg-primary mt-5"
              disabled={!isValid}
            >
              {isEditMode ? "Update Config" : "Add Config"}
            </Button>
            <Button variant="ghost" onClick={onClose} className="w-full">
              Cancel
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
