"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DeleteGroupDialog } from "@/app/app/settings/groups/page";
import { SquarePen } from "@/components/icons/list";

export interface group {
  id: string;
  name: string;
  new_group_name?: string;
}

// group Table Component
export function GroupTable({
  onDelete,
  handleEdit,
  groups,
}: {
  onDelete: (name: string) => void; 
  handleEdit: (group: group) => void;
  groups: group[];
}) {
  return (
    <div className="bg-white rounded-lg shadow p-4 overflow-x-auto">
      <div className="w-full overflow-x-auto">
        <table className="w-full table-fixed">
          <thead className="bg-gray-200 text-left text-xs uppercase align-middle">
            <tr>
              <th className="px-4 py-2 w-1/3">S.N.</th>
              <th className="px-4 py-2 w-1/3">Group Name</th>
              <th className="px-4 py-2 text-end w-1/3">Actions</th>
            </tr>
          </thead>
          <tbody>
            {groups?.length === 0 ? (
              <tr>
                {/* IMPORTANT: Adjust colSpan to match your actual column count (3) */}
                <td
                  colSpan={3}
                  className="px-1 sm:px-2 py-8 text-center text-blue-900 text-sm"
                >
                  Oops! No Group matched your search
                </td>
              </tr>
            ) : (
              groups?.map((group, index) => (
                <tr key={group?.id} className="border-b hover:bg-gray-50">
                  <td className="px-4 py-1 text-xs">{index + 1}</td>
                  <td className="px-4 py-1 text-xs truncate">{group?.name}</td>
                  <td className="px-4 py-1 text-xs">
                    <div className="flex items-center justify-end gap-1">
                      <Button
                        onClick={() => {
                          handleEdit(group);
                        }}
                        className="bg-green-500 hover:bg-green-600 text-white p-1 rounded-md h-7 w-7"
                      >
                        <SquarePen className="h-4 w-4" />
                      </Button>
                      <DeleteGroupDialog
                        name={group?.name}
                        onDelete={onDelete}
                      />
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}

// // group Row Component
// function UserTable({
//   group,
//   index,
//   handleEdit, 
//   onDelete, 
// }: {
//   group: group;
//   index: number;
//   handleEdit: (group: group) => void;
//   onDelete: (name: string) => void;
// }) {}
export function AddGroupForm({
  onClose,
  onSubmit,
}: {
  onSubmit: (groupname: group) => void;
  onClose: () => void;
}) {
  const [newgroup, setNewgroup] = useState({
    groupname: "",
  });
  const isformValid = newgroup?.groupname.trim() !== "";

  return (
    <>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 sm:p-6">
        <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm" />
        <div className="relative bg-white rounded-xl shadow-2xl overflow-hidden max-w-md md:max-w-lg w-full max-h-[90vh] flex flex-col">
          <div className="px-5 py-3 bg-gradient-to-r bg-primary text-white border-b">
            <h1 className="text-lg font-extrabold text-center">Add New Group</h1>
            <p className="text-blue-100 text-sm text-center">
              Create a new group with the required information.
            </p>
          </div>
          <form
            onSubmit={(e) => {
              e.preventDefault();
              onSubmit({
                // id: Date.now().toString(), // Ensure id is a string
                groupname: newgroup?.groupname.trim(), // Changed groupname to name to match interface
              });
              setNewgroup({
                groupname: "",
              });
            }}
            className="flex-grow p-5 grid grid-cols-1 gap-x-6 gap-y-5 overflow-y-auto custom-scrollbar"
          >
            <Input
              placeholder="Enter Group Name"
              value={newgroup?.groupname}
              onChange={(e) =>
                setNewgroup((prev) => ({ ...prev, groupname: e.target.value }))
              }
            />
            <div className="space-y-2">
            <Button type="submit" className="w-full bg-primary" disabled={!isformValid}>
              Add group
            </Button>
            <Button variant="outline" onClick={onClose} className="w-full">
              Cancel
            </Button>
            </div>
          </form>
          {/* <div className="mt-6 pt-4 border-t">
            <Button variant="outline" onClick={onClose} className="w-full">
              Cancel
            </Button>
          </div> */}
        </div>
      </div>
    </>
  );
}
