"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { NAS, NASFormData } from "@/types/nas";

interface NASFormProps {
  initialData?: NAS;
  onSubmit: (data: NASFormData) => void;
  onCancel?: () => void;
  isEdit?: boolean;
}

export default function NASForm({
  initialData,
  onSubmit,
  onCancel,
  isEdit = false,
}: NASFormProps) {
  const [formData, setFormData] = useState<NASFormData>({
    name: initialData?.name || "",
    nasname: initialData?.nasname || "", // Often used for IP/hostname
    secret: initialData?.secret || "",
    description: initialData?.description || "",
    vendor: initialData?.vendor || "",
  });

  const [errors, setErrors] = useState<Partial<NASFormData>>({});

  const handleChange = (field: keyof NASFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error for the specific field when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined })); // Use undefined to clear
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<NASFormData> = {};

    if (!formData?.name.trim()) {
      newErrors.name = "Device Name is required"; // Corrected field name
    }

    // Basic IP validation (can be more robust if needed)
    const ipRegex = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/;
    if (!formData?.nasname.trim()) {
      newErrors.nasname = "Device IP is required";
    } else if (!ipRegex.test(formData.nasname.trim())) {
      newErrors.nasname = "Please enter a valid IP address";
    }

    if (!formData?.secret.trim()) {
      newErrors.secret = "Secret is required";
    }

    if (!formData?.description.trim()) {
      newErrors.description = "Description is required";
    }

    if (!formData?.vendor.trim()) {
      newErrors.vendor = "Vendor is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const getChangedFields = (): Partial<NAS> => {
    const changed: Partial<NAS> = {};
    if (initialData?.id) {
      changed.id = initialData.id; // Include ID for existing data
    }

    // Compare formData with initialData to find changes
    if (formData.name.trim() !== initialData?.name) {
      changed.name = formData.name.trim();
    }
    if (formData.nasname.trim() !== initialData?.nasname) {
      changed.nasname = formData.nasname.trim();
    }
    if (formData.secret.trim() !== initialData?.secret) {
      // Only include secret if it was changed (or if it's a new entry)
      changed.secret = formData.secret.trim();
    }
    if (formData.description.trim() !== initialData?.description) {
      changed.description = formData.description.trim();
    }
    if (formData.vendor.trim() !== initialData?.vendor) {
      changed.vendor = formData.vendor.trim();
    }
    return changed;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      if (isEdit) {
        const changedData = getChangedFields();
        // Ensure you only submit if there are actual changes
        if (Object.keys(changedData).length > (initialData?.id ? 1 : 0)) {
          onSubmit(changedData as NASFormData); // Cast to NASFormData as per onSubmit's type
        } else {
          // If no changes were made in edit mode, just close the form
          onCancel?.();
        }
      } else {
        onSubmit(formData);
      }
    }
  };

  const formTitle = isEdit ? "Edit Network Device" : "Add New Network Device";
  const formDescription = isEdit
    ? `Update details for ${formData.name || "this device"}.`
    : "Register a new network device (NAS) with its details.";

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm" />
      <div className="relative bg-white rounded-xl shadow-2xl overflow-hidden max-w-md md:max-w-lg w-full max-h-[90vh] flex flex-col">
        
        <div className="px-5 py-6 bg-gradient-to-r bg-primary text-white border-b">
          
          <h1 className="text-lg font-extrabold text-center">{isEdit ? "Edit Device" : "Add Device"}</h1>
          <p className="text-blue-100 text-sm  text-center">
            {isEdit ? `Update device information for ${formData.name || "Unknown Device"}` : "Specify the details of the new device"}

            
            {/* <strong>{ formData.name || "Unknown Device"}</strong> */}
          </p>
          </div>
    <form onSubmit={handleSubmit} className="flex-grow p-10 grid grid-cols-1 gap-x-6 gap-y-4 overflow-y-auto custom-scrollbar">
        {/* NAS Name */}

        <div className="space-y-2">
          <Label htmlFor="name">Device Name *</Label>
          <Input
            id="name"
            type="text"
            value={formData.name}
            onChange={(e) => handleChange("name", e.target.value)}
            placeholder="Enter Device name (e.g. Cambium)"
            className={errors.name ? "border-red-500" : ""}
          />
          {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
        </div>

          {/* Device IP */}
          <div className="space-y-1">
            <Label
              htmlFor="nasname"
              className="block text-xs font-semibold text-gray-700"
            >
              Device IP
            </Label>
            <Input
              id="nasname"
              type="text"
              value={formData.nasname}
              onChange={(e) => handleChange("nasname", e.target.value)}
              placeholder="e.g. ***********"
              className={
                errors.nasname ? "border-red-500 focus:ring-red-500" : ""
              }
            />
            {errors.nasname && (
              <p className="text-xs text-red-500 mt-1">{errors.nasname}</p>
            )}
          </div>

          {/* Secret */}
          <div className="space-y-1">
            <Label
              htmlFor="secret"
              className="block text-xs font-semibold text-gray-700"
            >
              Secret
            </Label>
            <Input
              id="secret"
              type="password"
              value={formData.secret}
              onChange={(e) => handleChange("secret", e.target.value)}
              placeholder="Enter RADIUS secret key"
              className={
                errors.secret ? "border-red-500 focus:ring-red-500" : ""
              }
            />
            {errors.secret && (
              <p className="text-xs text-red-500 mt-1">{errors.secret}</p>
            )}
          </div>

          {/* Vendor */}
          <div className="space-y-1">
            <Label
              htmlFor="vendor"
              className="block text-xs font-semibold text-gray-700"
            >
              Vendor
            </Label>
            <select
              id="vendor"
              value={formData.vendor}
              onChange={(e) => handleChange("vendor", e.target.value)}
              className={`w-full p-2.5 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 ease-in-out text-gray-800 ${
                errors.vendor ? "border-red-500" : "border-gray-300"
              }`}
            >
              <option value="">Select Vendor</option>
              <option value="mikrotik">Mikrotik</option>
              <option value="cisco">Cisco</option>
              <option value="aruba">Aruba</option>
              <option value="ubiquiti">Ubiquiti</option>
              <option value="huawei">Huawei</option>
              <option value="other">Other</option>
            </select>
            {errors.vendor && (
              <p className="text-xs text-red-500 mt-1">{errors.vendor}</p>
            )}
          </div>

      {/* Description */}
      <div className="space-y-2">
        <Label htmlFor="description">Description *</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleChange("description", e.target.value)}
          placeholder="Enter description"
          rows={4}
          className={errors.description ? "border-red-500" : ""}
        />
        {errors.description && (
          <p className="text-sm text-red-500">{errors.description}</p>
        )}
      </div>

      {/* Form Actions */}
      <div className="flex  gap-4 pt-4">
        <Button type="submit" className=" flex-1">
          {isEdit ? "Update Device" : "Add Device"}
        </Button>
        {onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            className="flex-1"
          >
            Cancel
          </Button>
        )}
      </div>
    </form>
    </div>
    </div>
  );
}
