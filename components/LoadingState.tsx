"use client";

import React from 'react';

interface LoadingStateProps {
  text?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'spinner' | 'dots' | 'pulse';
}

export const LoadingSpinner: React.FC<{ size?: 'sm' | 'md' | 'lg' }> = ({ 
  size = 'md' 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8', 
    lg: 'w-12 h-12'
  };

  return (
    <div className={`${sizeClasses[size]} animate-spin rounded-full border-2 border-gray-300 border-t-blue-600`} />
  );
};

export const LoadingDots: React.FC<{ size?: 'sm' | 'md' | 'lg' }> = ({ 
  size = 'md' 
}) => {
  const sizeClasses = {
    sm: 'w-1 h-1',
    md: 'w-2 h-2',
    lg: 'w-3 h-3'
  };

  return (
    <div className="flex space-x-1">
      <div className={`${sizeClasses[size]} bg-blue-600 rounded-full animate-bounce-slow`} />
      <div className={`${sizeClasses[size]} bg-blue-600 rounded-full animate-bounce-medium`} />
      <div className={`${sizeClasses[size]} bg-blue-600 rounded-full animate-bounce-fast`} />
    </div>
  );
};

export const LoadingPulse: React.FC<{ size?: 'sm' | 'md' | 'lg' }> = ({ 
  size = 'md' 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  return (
    <div className={`${sizeClasses[size]} bg-blue-600 rounded-full animate-pulse`} />
  );
};

export const LoadingState: React.FC<LoadingStateProps> = ({ 
  text = 'Loading...', 
  size = 'md',
  variant = 'spinner' 
}) => {
  const LoadingComponent = {
    spinner: LoadingSpinner,
    dots: LoadingDots,
    pulse: LoadingPulse
  }[variant];

  return (
    <div className="flex flex-col items-center justify-center p-6 space-y-3">
      <LoadingComponent size={size} />
      <p className="text-gray-600 text-sm">{text}</p>
    </div>
  );
};

// Inline loading for buttons
export const ButtonLoading: React.FC<{ text?: string }> = ({ 
  text = 'Loading...' 
}) => (
  <div className="flex items-center space-x-2">
    <LoadingSpinner size="sm" />
    <span>{text}</span>
  </div>
);

// Table loading state
export const TableLoading: React.FC<{ 
  rows?: number; 
  columns?: number;
}> = ({ 
  rows = 5, 
  columns = 4 
}) => (
  <div className="w-full">
    {Array.from({ length: rows }).map((_, rowIndex) => (
      <div key={rowIndex} className="flex space-x-4 p-4 border-b border-gray-200">
        {Array.from({ length: columns }).map((_, colIndex) => (
          <div 
            key={colIndex} 
            className="h-4 bg-gray-300 rounded animate-pulse flex-1"
          />
        ))}
      </div>
    ))}
  </div>
);

// Card loading state
export const CardLoading: React.FC = () => (
  <div className="bg-white p-6 rounded-lg shadow-md border animate-pulse">
    <div className="h-4 bg-gray-300 rounded w-3/4 mb-4" />
    <div className="h-3 bg-gray-300 rounded w-1/2 mb-2" />
    <div className="h-3 bg-gray-300 rounded w-5/6" />
  </div>
);

export default LoadingState;