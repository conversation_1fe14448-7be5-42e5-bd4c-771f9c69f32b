"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

interface DepartmentFormProps {
  onSubmit: (packageData: any) => Promise<boolean>;
  onCancel?: () => void;
  initialData?: any;
  isEdit?: boolean;
}

export default function DepartmentForm({
  onSubmit,
  onCancel,
  initialData,
  isEdit = false,
}: DepartmentFormProps) {
  const [form, setForm] = useState({
    package_name: initialData?.package_name || "",
    upload: initialData?.upload || "",
    download: initialData?.download || "",
    status: initialData?.status || "Active",
    vlan: initialData?.vlan || "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    setForm({
      package_name: initialData?.package_name || "",
      upload: initialData?.upload || "",
      download: initialData?.download || "",
      status: initialData?.status || "Active",
      vlan: initialData?.vlan || "",
    });
  }, [initialData]);

  const isFormValid = () => {
    return (
      form.package_name.trim() !== "" &&
      form.upload.trim() !== "" &&
      form.download.trim() !== ""
    );
  };

  const handleChange = (field: keyof typeof form, value: string) => {
    setForm((prevForm) => ({ ...prevForm, [field]: value }));
  };

  const getChanged = () => {
    const changed: Record<string, any> = { id: initialData?.id };
    for (const key in form) {
      if (
        Object.prototype.hasOwnProperty.call(form, key) &&
        form[key] !== initialData?.[key]
      ) {
        changed[key] = form[key];
      }
    }
    return changed;
  };

  const resetForm = () => {
    setForm({
      package_name: "",
      upload: "",
      download: "",
      status: "Active",
      vlan: "",
    });
  };

  const handleSubmit = async () => {
    if (!isFormValid() || isSubmitting) {
      return;
    }

    setIsSubmitting(true);

    let payloadToSubmit;
    if (isEdit) {
      payloadToSubmit = getChanged();
    } else {
      payloadToSubmit = form;
    }

    const success = await onSubmit?.(payloadToSubmit);
    if (success) {
      resetForm();
    }

    setIsSubmitting(false);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      
      <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm" />
      <div className="relative bg-white rounded-xl shadow-2xl overflow-hidden max-w-md md:max-w-[37rem] w-full max-h-[90vh] flex flex-col">
        
        <div className="px-5 py-6 bg-gradient-to-r bg-primary text-white border-b">
          
          <h1 className="text-lg font-extrabold text-center">
            {isEdit ? "Edit Department" : "Add Department"}
          </h1>
          </div>
        <div className="flex-grow p-10 grid grid-cols-1 gap-x-6 gap-y-5 overflow-y-auto custom-scrollbar">
          

            <div className="text-xs grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Department Name *</Label>
                <Input
                  type="text"
                  value={form.package_name}
                  onChange={(e) => handleChange("package_name", e.target.value)}
                  placeholder="e.g. Management"
                />
              </div>

              <div>
                <div className="space-y-2">
                  <Label>Upload Speed *</Label>
                  <Input
                    type="text"
                    value={form.upload}
                    onChange={(e) => handleChange("upload", e.target.value)}
                    placeholder="e.g. 100M"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Download Speed *</Label>
                <Input
                  type="text"
                  value={form.download}
                  onChange={(e) => handleChange("download", e.target.value)}
                  placeholder="e.g. 100M"
                />
              </div>

              <div className="space-y-2">
                <Label>VLAN</Label>
                <Input
                  type="text"
                  value={form.vlan}
                  onChange={(e) => handleChange("vlan", e.target.value)}
                  placeholder="e.g. 45"
                />
              </div>

              <div className="space-y-2">
                <Label>Status *</Label>
                <div className="flex gap-4">
                  {/* Active Button */}
                  <button
                    type="button"
                    aria-pressed={form.status === "Active"}
                    className={`w-20 h-8 border-2 rounded-lg flex items-center justify-center
        ${
          form.status === "Active"
            ? "border-blue-600 bg-blue-600 text-white"
            : "border-gray-300 bg-white text-black"
        }
        hover:border-blue-400 transition-colors font-medium`}
                    onClick={() => handleChange("status", "Active")}
                  >
                    Active
                  </button>

                  <button
                    type="button"
                    aria-pressed={form.status === "Inactive"}
                    className={`w-20 h-8 border-2 rounded-lg flex items-center justify-center
        ${
          form.status === "Inactive"
            ? "border-blue-600 bg-blue-600 text-white"
            : "border-gray-300 bg-white text-black"
        }
        hover:border-blue-400 transition-colors font-medium`}
                    onClick={() => handleChange("status", "Inactive")}
                  >
                    Inactive
                  </button>
                </div>
              </div>
            </div>

          <div className="flex gap-4 pt-4">
            <Button
              onClick={handleSubmit}
              className="bg-primary "
              disabled={!isFormValid() || isSubmitting}
            >
              {isSubmitting ? (isEdit ? "Updating..." : "Submitting...") : (isEdit ? "Update Department" : "Submit Department")}
            </Button>
            {onCancel && (
              <Button onClick={onCancel} variant="outline" disabled={isSubmitting}>
                Cancel
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
