"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import DepartmentForm from "@/components/department/department-form";
import apiClient from "@/lib/apiClient";
import { toast } from "sonner";

export default function DepartmentDetails() {
  const [showForm, setShowForm] = useState(false);

  const handleSubmit = async (packageData: any) => {
    try {
      await apiClient.post("/package", packageData);
      toast.success("Department created successfully");
      setShowForm(false);
      return true;
    } catch (error: any) {
      console.error("Failed to create department:", error);
      toast.error(error.message || "Failed to create department");
      return false;
    }
  };

  const handleCancel = () => {
    setShowForm(false);
  };

  return (
    <div className="p-5  mx-auto bg-white rounded-lg">
      <div className="flex justify-between items-center  mb-6">
        <Button size="sm" onClick={() => setShowForm(!showForm)}>
          {showForm ? "Cancel" : "Add Department"}
        </Button>
      </div>

      {showForm ? (
        <div className="mb-6">
          <DepartmentForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
          />
        </div>
      ) : null}
    </div>
  );
}
