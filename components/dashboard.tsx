"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";
import apiClient from "@/lib/apiClient";
import { toast } from "sonner";
import { format, startOfHour, differenceInHours } from "date-fns";

import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";

interface CustomerType {
  id: number;
  first_name: string;
  last_name: string;
  username: string;
  password: string;
  contact: string[];
  email: string;
  address: string;
  status: string;
  package: string;
  registered: string;
  expiration: string;
  organization?: string;
}
interface AnalyticsEntry {
  id: number;
  count: number;
  data: Array<{
    username?: string;
    acctstarttime?: string;
    acctinputoctets?: number;
    acctoutputoctets?: number;
  }>;
  created_at: number;
  inputMbps?: number;
  outputMbps?: number;
}

interface GuestType {
  id: number;
  data: {
    name: string;
    email: string;
    company: string;
  };
}

type CustomerFilter = "All" | "Active" | "Inactive" | "Expired";

export default function DashboardPage() {
  const [currentFilter, setCurrentFilter] = useState<CustomerFilter>("All");
  const router = useRouter();
  const access_token = Cookies.get("accessToken");

  const [allCustomers, setAllCustomers] = useState<CustomerType[]>([]);
  const [clientCount, setClientCount] = useState<AnalyticsEntry[]>([]);
  const [guests, setGuests] = useState<GuestType[]>([]);
  const [loadingCustomers, setLoadingCustomers] = useState(true);
  const [loadingGuests, setLoadingGuests] = useState(true);
  const [errorCustomers, setErrorCustomers] = useState<string | null>(null);
  const [errorGuests, setErrorGuests] = useState<string | null>(null);
  const [selectedHours, setSelectedHours] = useState(1);
  const [startDate, setStartDate] = useState<Date | null>(new Date());
  const [endDate, setEndDate] = useState<Date | null>(new Date());

  // Active clients count from last data point
  const activeCustomers = clientCount.at(-1)?.count ?? 0;

  useEffect(() => {
    if (!access_token) {
      setLoadingCustomers(false);
      return;
    }
    setLoadingCustomers(true);
    setErrorCustomers(null);
    apiClient
      .get("/customer", {
        headers: { Authorization: `Bearer ${access_token}` },
        cache: "no-store",
      })
      .then((response) => {
        // Backend returns: { success: true, message: "List Customer", data: [...] }
        // The apiClient response interceptor extracts the data property
        setAllCustomers(response?.data || []);
      })
      .catch((error) => {
        console.error("Customer fetch error:", error);
        setErrorCustomers(error.message || "Failed to load customer data.");
      })
      .finally(() => setLoadingCustomers(false));
  }, [access_token]);

  useEffect(() => {
    const now = new Date();
    const start = new Date(now.getTime() - selectedHours * 60 * 60 * 1000);
    setStartDate(start);
    setEndDate(now);

    const formattedStart = format(start, "yyyy-MM-dd HH:mm:ss");
    const formattedEnd = format(now, "yyyy-MM-dd HH:mm:ss");

    if (!access_token) return;

    apiClient
      .post(
        "/analytics",
        { created_from: formattedStart, created_to: formattedEnd },
        {
          headers: { Authorization: `Bearer ${access_token}` },
        }
      )
      .then((response) => {
        // Backend returns: { success: true, message: "Analytics", data: [...] }
        // The apiClient response interceptor extracts the data property
        const dataWithTimestamp = (response?.data?.data || []).map((entry) => ({
          ...entry,
          created_at: new Date(entry.created_at).getTime(),
        }));
        console.log(`Analytics data for ${selectedHours}h:`, dataWithTimestamp);
        setClientCount(dataWithTimestamp);
      })
      .catch((error) => {
        console.error("Analytics fetch error:", error);
        toast.error(error.message || "Failed to fetch client analytics");
      });
  }, [selectedHours, access_token]);

  useEffect(() => {
    if (!access_token) {
      setLoadingGuests(false);
      return;
    }
    setLoadingGuests(true);
    setErrorGuests(null);
    apiClient
      .get("/guest/voucher/?type=guest", {
        headers: { Authorization: `Bearer ${access_token}` },
        cache: "no-store",
      })
      .then((response) => {
        // Backend returns: { success: true, data: [...] }
        // The apiClient response interceptor extracts the data property
        setGuests(response?.data?.data || []);
      })
      .catch((error) => {
        console.error("Guest fetch error:", error);
        setErrorGuests(error.message || "Failed to load guest data.");
      })
      .finally(() => setLoadingGuests(false));
  }, [access_token]);

  // Generate fallback data when no real data is available
  const generateFallbackData = (): AnalyticsEntry[] => {
    const now = new Date();
    const start = new Date(now.getTime() - selectedHours * 60 * 60 * 1000);
    const fallbackData: AnalyticsEntry[] = [];

    let intervalMs: number;
    if (selectedHours === 1) {
      intervalMs = 5 * 60 * 1000; // 5 minutes
    } else if (selectedHours === 6) {
      intervalMs = 30 * 60 * 1000; // 30 minutes
    } else {
      intervalMs = 60 * 60 * 1000; // 1 hour
    }

    // Round start time to the nearest interval boundary to align with ticks
    const roundedStart = Math.floor(start.getTime() / intervalMs) * intervalMs;
    const roundedEnd = Math.ceil(now.getTime() / intervalMs) * intervalMs;

    for (let ts = roundedStart; ts <= roundedEnd; ts += intervalMs) {
      fallbackData.push({
        id: 0,
        count: 0,
        data: [],
        created_at: ts,
        inputMbps: 0,
        outputMbps: 0,
      });
    }

    return fallbackData;
  };

  // Generate ticks at intervals based on clientCount data range
  const generateTicks = (): number[] => {
    const dataToUse = clientCount.length > 0 ? clientCount : generateFallbackData();
    if (!dataToUse.length) return [];

    const minTime = new Date(Math.min(...dataToUse.map((d: AnalyticsEntry) => d.created_at)));
    const maxTime = new Date(Math.max(...dataToUse.map((d: AnalyticsEntry) => d.created_at)));
    const ticks: number[] = [];

    let intervalMs: number;

    if (selectedHours === 1) {
      intervalMs = 5 * 60 * 1000; // 5 minutes
    } else if (selectedHours === 6) {
      intervalMs = 30 * 60 * 1000; // 30 minutes
    } else {
      intervalMs = 60 * 60 * 1000; // 1 hour
    }

    // Use the same rounding logic as fallback data generation
    const start = Math.floor(minTime.getTime() / intervalMs) * intervalMs;
    const end = Math.ceil(maxTime.getTime() / intervalMs) * intervalMs;

    for (let ts = start; ts <= end; ts += intervalMs) {
      ticks.push(ts);
    }

    return ticks;
  };

  const ticks = generateTicks();

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload?.length) {
      const point = payload.reduce((acc: any, item: any) => {
        acc[item.dataKey] = item.payload[item.dataKey];
        acc.created_at = item.payload.created_at;
        return acc;
      }, {});

      return (
        <div className="bg-white p-2 border border-gray-300 rounded shadow text-xs">
          <p className="text-gray-600">
            {new Date(point.created_at).toLocaleString([], {
              day: "2-digit",
              year: "2-digit",
              month: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
            })}
          </p>
          {point.count !== undefined && (
            <p className="text-black font-semibold">
              {point.count} Online client{point.count > 1 ? "s" : ""}
            </p>
          )}
          {point.inputMbps !== undefined && (
            <p className="text-black font-semibold">
              Input (Upload): {Number(point.inputMbps).toFixed(2)} Mbps
            </p>
          )}
          {point.outputMbps !== undefined && (
            <p className="text-black font-semibold">
              Output (Download): {Number(point.outputMbps).toFixed(2)} Mbps
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="flex flex-1 overflow-hidden">
      <main className="flex-1 overflow-auto">
        {/* Stats Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mb-3">
          {/* Active Clients Card */}
          <div
            className="bg-white text-green-700 p-4 rounded-lg shadow-md border border-green-300 cursor-pointer hover:shadow-lg hover:bg-green-50 transition-all duration-200"
            onClick={() => router.push('/app/analytics?type=activesessions&tab=users')}
          >
            <div>
              <h3 className="text-sm font-medium text-green-600">
                Online Clients
              </h3>
              <p className="text-2xl font-bold text-green-800">
                {activeCustomers}
              </p>
            </div>
          </div>

          {/* Total Staff Card */}
          <div className="bg-white text-blue-700 p-4 rounded-lg shadow-md border border-blue-300">
            <div>
              <h3 className="text-sm font-medium text-blue-600">Total Staff</h3>
              <p className="text-2xl font-bold text-blue-800">
                {allCustomers.length}
              </p>
            </div>
          </div>

          {/* Total Guests Card */}
          <div className="bg-white text-purple-700 p-4 rounded-lg shadow-md border border-purple-300">
            <div>
              <h3 className="text-sm font-medium text-purple-600">
                Total Guests
              </h3>
              <p className="text-2xl font-bold text-purple-800">
                {guests.length}
              </p>
            </div>
          </div>

          {/* <div className="bg-white text-orange-700 p-4 rounded-lg shadow-md border border-orange-300">
            <div>
              <h3 className="text-sm font-medium text-orange-600">
                Bandwidth Usage
              </h3>
              <p className="text-2xl font-bold text-orange-800">--</p>
            </div>
          </div> */}

        </div>

        <div className="bg-white p-4 rounded-lg shadow-md mb-3 border border-indigo-300">
          <label className="block text-sm font-medium mb-3 text-indigo-700">
            Select Time Range
          </label>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setSelectedHours(1)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                selectedHours === 1
                  ? "bg-blue-600 text-white shadow-md"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              1 Hr
            </button>
            <button
              onClick={() => setSelectedHours(6)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                selectedHours === 6
                  ? "bg-blue-600 text-white shadow-md"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              6 Hrs
            </button>
            <button
              onClick={() => setSelectedHours(12)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                selectedHours === 12
                  ? "bg-blue-600 text-white shadow-md"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              12 Hrs
            </button>
            <button
              onClick={() => setSelectedHours(24)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                selectedHours === 24
                  ? "bg-blue-600 text-white shadow-md"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              1 Day
            </button>
            <button
              onClick={() => setSelectedHours(120)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                selectedHours === 120
                  ? "bg-blue-600 text-white shadow-md"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              5 Days
            </button>
            <button
              onClick={() => setSelectedHours(168)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                selectedHours === 168
                  ? "bg-blue-600 text-white shadow-md"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              1 Week
            </button>
          </div>
        </div>

        {/* Charts Row */}
        <div className="grid grid-cols-2 gap-3 mb-3">
          {/* Online Client Graph */}
          <div className="bg-white p-2 rounded-lg h-[280px] flex items-center justify-center border border-green-300">

            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={clientCount.length > 0 ? clientCount : generateFallbackData()}
                margin={{ top: 0, right: 30, left: 0, bottom: 10 }}
              >
                <CartesianGrid stroke="#f0f0f0" strokeDasharray="1 1" />
                <XAxis
                  dataKey="created_at"
                  type="number"
                  scale="time"
                  domain={clientCount.length > 0 ? ["dataMin", "dataMax"] : ["auto", "auto"]}
                  ticks={ticks}
                  tickFormatter={(tick) => format(new Date(tick), "HH:mm")}
                  tick={{ fontSize: 12, fill: "#000000ff" }}
                  label={{
                    value: "Time",
                    position: "insideBottom",
                    offset: -5,
                    style: { fontSize: 12, fill: "#000000ff" },
                  }}
                  minimumTickGap={10}
                />
                <YAxis
                  domain={[0, "auto"]}
                  tickFormatter={(tick) => (tick === 0 ? "0" : tick)}
                  tick={{ fontSize: 12, fill: "#000000ff" }}
                  allowDecimals={false}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend
                  verticalAlign="top"
                  payload={[{ value: "Online Client", color: "#16a34a" }]}
                />
                <Line
                  type="step"
                  dataKey="count"
                  stroke="#16a34a"
                  strokeWidth={2}
                  dot={false}
                  activeDot={{
                    r: 4,
                    fill: "#16a34a",
                    stroke: "#fff",
                    strokeWidth: 2,
                  }}
                />
              </LineChart>
            </ResponsiveContainer>

          </div>

          {/* Bandwidth Graph */}
          <div className="bg-white p-2 rounded-lg h-[280px] flex items-center justify-center border border-yellow-300">

            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={clientCount.length > 0 ? clientCount : generateFallbackData()} margin={{ top: 0, right: 30, left: 0, bottom: 10 }}>
                <CartesianGrid stroke="#f0f0f0" strokeDasharray="1 1" />
                <XAxis
                  dataKey="created_at"
                  type="number"
                  scale="time"
                  domain={clientCount.length > 0 ? ["dataMin", "dataMax"] : ["auto", "auto"]}
                  ticks={ticks}
                  tickFormatter={(tick) => format(new Date(tick), "HH:mm")}
                  tick={{ fontSize: 12, fill: "#000000ff" }}
                  label={{ value: "Time", position: "insideBottom", offset: -5, style: { fontSize: 12, fill: "#000000ff" } }}
                />
                <YAxis tickFormatter={(tick) => (tick === 0 ? "" : tick)} tick={{ fontSize: 12, fill: "#000000ff" }} allowDecimals={false} />
                <Tooltip content={<CustomTooltip />} />
                <Legend verticalAlign="top" payload={[{ value: "Download", color: "#f59e0b" }, { value: "Upload", color: "#1fd906ff" }]} />
                <Line
                  type="monotone"
                  dataKey="inputMbps"
                  fill="#1fd906ff"
                  stroke="#1fd906ff"
                  strokeWidth={2}
                  dot={false}
                  activeDot={{ r: 4, fill: "#1fd906ff", stroke: "#fff", strokeWidth: 2 }}
                />
                <Line
                  type="monotone"
                  dataKey="outputMbps"
                  fill="#f59e0b"
                  stroke="#f59e0b"
                  strokeWidth={2}
                  dot={false}
                  activeDot={{ r: 4, fill: "#f59e0b", stroke: "#fff", strokeWidth: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>

          </div>
        </div>

        {/* Tables Row */}
        <div className="grid grid-cols-2 gap-3 mb-3">
          {/* Recently Connected Guests Table */}
          <div className="bg-white p-4 rounded-lg shadow-md border border-blue-300">
            <h2 className="font-semibold mb-2 text-blue-700 rounded">
              Recently Connected Guests
            </h2>
            <div className="overflow-x-auto">
              <table className="min-w-full leading-normal text-xs">
                <thead>
                  <tr className="text-white uppercase text-left text-xs tracking-wider border-b border-blue-300 bg-blue-600">
                    <th className="py-1 px-4">Name</th>
                    <th className="py-1 px-4">Email</th>
                    <th className="py-1 px-4">Company</th>
                  </tr>
                </thead>
                <tbody>
                  {loadingGuests ? (
                    <tr>
                      <td
                        colSpan={3}
                        className="text-center py-6 text-blue-500"
                      >
                        Loading guest data...
                      </td>
                    </tr>
                  ) : errorGuests ? (
                    <tr>
                      <td colSpan={3} className="text-center py-6 text-red-600">
                        {errorGuests}
                      </td>
                    </tr>
                  ) : guests.length > 0 ? (
                    guests.slice(0, 7).map((guest) => (
                      <tr
                        key={guest.id}
                        className="border-b border-blue-200 hover:bg-blue-50"
                      >
                        <td className="py-1 px-4 text-blue-700 whitespace-nowrap">
                          {guest.data.name}
                        </td>
                        <td className="py-1 px-4 text-black whitespace-nowrap">
                          {guest.data.email}
                        </td>
                        <td className="py-1 px-4 text-black whitespace-nowrap">
                          {guest.data.company}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td
                        colSpan={3}
                        className="text-center py-6 text-blue-500"
                      >
                        No recent guest connections found.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg shadow-md border border-gray-300 flex items-center justify-center text-gray-600">
            <span className="font-medium">Coming Soon</span>
          </div>
        </div>

        {/* --- Second Row with Recently Connected Users Table --- */}
        <div className="w-full bg-white p-4 rounded-lg shadow-md border border-purple-300">
          <h2 className="font-semibold mb-2 text-purple-700 rounded">
            Recently Connected Users
          </h2>
          <div className="overflow-x-auto">
            <table className="min-w-full leading-normal text-sm">
              <thead>
                <tr className="bg-purple-600 text-white uppercase text-left text-xs tracking-wider border-b border-purple-300">
                  <th className="py-1 px-4">Name</th>
                  <th className="py-1 px-4">Email</th>
                  <th className="py-1 px-4">Status</th>
                </tr>
              </thead>
              <tbody>
                {loadingCustomers ? (
                  <tr>
                    <td
                      colSpan={3}
                      className="text-center py-6 text-purple-500"
                    >
                      Loading user data...
                    </td>
                  </tr>
                ) : errorCustomers ? (
                  <tr>
                    <td colSpan={3} className="text-center py-6 text-red-600">
                      {errorCustomers}
                    </td>
                  </tr>
                ) : allCustomers.length > 0 ? (
                  allCustomers.slice(0, 7).map((customer) => (
                    <tr
                      key={customer.id}
                      className="border-b border-purple-200 hover:bg-purple-50"
                    >
                      <td className="py-1 px-4 text-purple-700 whitespace-nowrap">
                        {customer.first_name} {customer.last_name}
                      </td>
                      <td className="py-1 px-4 text-black whitespace-nowrap">
                        {customer.email}
                      </td>
                      <td
                        className={`py-1 px-4 font-medium whitespace-nowrap ${
                          customer.status === "Active"
                            ? "text-green-600"
                            : customer.status === "Inactive"
                            ? "text-yellow-600"
                            : "text-red-600"
                        }`}
                      >
                        {customer.status}
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan={3}
                      className="text-center py-6 text-purple-500"
                    >
                      No recent user connections found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </main>
    </div>
  );
}
