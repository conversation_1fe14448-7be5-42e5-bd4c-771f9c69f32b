import React from "react";

interface CustomerBasicInfoProps {
  customer: any;
  showPassword: boolean;
}

const CustomerBasicInfo: React.FC<CustomerBasicInfoProps> = ({
  customer,
  showPassword,
}) => (
  <div className="bg-white p-4 rounded-lg border shadow-sm text-xs border-blue-500">
    <h3 className=" font-semibold mb-2 bg-blue-200 text-blue-900 border-blue-500 border-l-4 p-2 rounded">
      Staff Details
    </h3>
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <tbody className="bg-white divide-y divide-gray-200">
          <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Staff Name
            </td>
            <td className="px-2 py-1 whitespace-nowrap text-gray-900">
              {`${customer?.first_name} ${customer?.last_name}` || "N/A"}
            </td>
          </tr>
          <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Username
            </td>
            <td className="px-2 py-1 whitespace-nowrap text-gray-900">
              {customer?.username || "N/A"}
            </td>
          </tr>
          <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Password
            </td>
            <td className="px-2 py-1 whitespace-nowrap text-gray-900">
              {customer?.password ? (
                <div className="flex items-center">
                  <span className="mr-2">
                    {showPassword
                      ? customer?.password
                      : "*".repeat(customer.password.length)}
                  </span>
                  {/* <button
                    onClick={onTogglePassword}
                    className="text-gray-500 hover:text-gray-700 focus:outline-none"
                    aria-label={
                      showPassword ? "Hide password" : "Show password"
                    }
                  >
                    {showPassword ? (
                      <EyeOff className="w-5 h-5" />
                    ) : (
                      <Eye className="w-5 h-5" />
                    )}
                  </button> */}
                </div>
              ) : (
                "N/A"
              )}
            </td>
          </tr>
          <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Status
            </td>
            <td className="px-2 py-1 whitespace-nowrap text-gray-900">
              {customer?.status || "N/A"}
            </td>
          </tr>
          <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Email
            </td>
            <td className="px-2 py-1 whitespace-nowrap text-gray-900">
              {customer?.email || "N/A"}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
);

export default CustomerBasicInfo;
