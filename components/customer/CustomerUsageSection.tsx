// @/components/customer/CustomerUsageSection.tsx
import React from "react";
import { Button } from "@/components/ui/button";
import { RadAcctSession } from "@/types/interface-type";

interface CustomerUsageSectionProps {
  initialStartDate: string; // New: initial value for the start date picker
  initialEndDate: string; // New: initial value for the end date picker
  setInitialStartDate: (date: string) => void; // New: setter for start date picker
  setInitialEndDate: (date: string) => void; // New: setter for end date picker
  handleDateSubmit: (startDate: string, endDate: string) => void; // Now takes startDate and endDate
  handleDateReset: () => void; // NEW: Prop for the reset function
  filteredUsageByDate: RadAcctSession[];
  isLoading: boolean; // Loading state from parent
}

const CustomerUsageSection: React.FC<CustomerUsageSectionProps> = ({
  initialStartDate,
  initialEndDate,
  setInitialStartDate,
  setInitialEndDate,
  handleDateSubmit,
  handleDateReset, // Destructure the new prop
  filteredUsageByDate,
  isLoading,
}) => {
  return (
    <div className="space-y-4 sm:p-0">
      {/* Date Range Selector */}
      <div className="flex flex-col sm:flex-row items-center justify-center sm:justify-start space-y-2 sm:space-y-0 sm:space-x-4">
        
        {/* Adjusted padding */}
        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full sm:w-auto">
          <input
            type="date"
            value={initialStartDate} // Use initialStartDate for value
            onChange={(e) => setInitialStartDate(e.target.value)} // Use setInitialStartDate for onChange
            className="p-2 border h-9 border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 w-full text-sm" /* Adjusted height */
          />
          <span className="flex items-center justify-center sm:justify-start text-gray-700">
            -
          </span>
          <input
            type="date"
            value={initialEndDate} // Use initialEndDate for value
            onChange={(e) => setInitialEndDate(e.target.value)} // Use setInitialEndDate for onChange
            className="p-2 border h-9 border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 w-full text-sm" /* Adjusted height */
          />
        </div>
        <div className="flex space-x-2 w-full sm:w-auto">
          
          {/* NEW: Wrapper for buttons */}
          <Button
            size="sm"
            onClick={() => handleDateSubmit(initialStartDate, initialEndDate)}
            className="px-4 py-2 w-full sm:w-auto text-sm h-9 rounded-full" /* Adjusted height */
            disabled={isLoading}
          >
            {isLoading ? "Loading..." : "Submit"}
          </Button>
          <Button
            size="sm"
            variant="outline" // Use an outline variant for a secondary action
            onClick={handleDateReset} // Call the new reset handler
            className="px-4 py-2 w-full sm:w-auto text-sm h-9 rounded-full" /* Adjusted height */
            disabled={isLoading}
          >
            Reset
          </Button>
        </div>
      </div>
      {/* Usage Table */}
      <div className="overflow-x-auto bg-white rounded-lg shadow-md">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-100">
            <tr>
              <th
                scope="col"
                className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
              >
                Start Time
              </th>
              <th
                scope="col"
                className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
              >
                End Time
              </th>
              <th
                scope="col"
                className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
              >
                Session Time
              </th>
              <th
                scope="col"
                className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
              >
                Upload
              </th>
              <th
                scope="col"
                className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
              >
                Download
              </th>
              <th
                scope="col"
                className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
              >
                IP Address
              </th>
              <th
                scope="col"
                className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
              >
                MAC Address
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {isLoading ? (
              <tr>
                <td colSpan={8} className="px-3 py-4 text-center text-gray-500">
                  Loading usage data...
                </td>
              </tr>
            ) : filteredUsageByDate.length > 0 ? (
              filteredUsageByDate.map((row, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                    {new Date(row.acctstarttime).toLocaleString("en-US", {
                      year: "numeric",
                      month: "2-digit",
                      day: "2-digit",
                      hour: "2-digit",
                      minute: "2-digit",
                      second: "2-digit",
                      hour12: false, // 24-hour format
                    })}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                    {row.acctstoptime
                      ? new Date(row.acctstoptime).toLocaleString("en-US", {
                          year: "numeric",
                          month: "2-digit",
                          day: "2-digit",
                          hour: "2-digit",
                          minute: "2-digit",
                          second: "2-digit",
                          hour12: false, // 24-hour format
                        })
                      : "N/A"}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                    {row.acctsessiontime}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                    {row.acctinputoctets}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                    {row.acctoutputoctets}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                    {row.framedipaddress}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                    {row.callingstationid}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={8} className="px-3 py-4 text-center text-gray-500">
                  No usage data found for the selected date range or invalid
                  date range.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default CustomerUsageSection;
