"use client";

import React from "react";
import { useState, useRef, useEffect } from "react";
import { Input } from "@/components/ui/input";
import Link from "next/link";
// import { useSearchParams } from "next/navigation";
import {
  // UserAdd,
  // SquarePen,
  ChevronRight,
  ChevronLeft,
  // Plug,
  Trash2,
} from "@/components/icons/list";
// import ISPCustomerForm from "@/components/customer/isp-customer-form";
import CustomerView from "@/components/view-customer";
// import CustomerEdit from "@/components/customer/external-customer-edit";
import { Button } from "@/components/ui/button";
import Cookies from "js-cookie";
import apiClient from "@/lib/apiClient";
import { usePathname } from "next/navigation";
import { useAuth } from "@/context/AuthContext";
import { toast } from "sonner";





export default function ExternalCustomersMap(): CustomerFormProps {
  interface Customer {
    id: number;
    first_name: string;
    last_name: string;
    username: string;
    password: string;
    email: string;
    status: string;
    package: string;
    pack_id?: string;
  }

  interface department {
    id: number;
    package_name: string;
  }
  const [showForm, setShowForm] = useState(false); // true if ADD form or EDIT form is active
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>();
  const [currentPage, setCurrentPage] = useState(1);
  const [currentFilter, setCurrentFilter] = useState<CustomerFilter>("All");
  const [selectedItemsPerPageValue, setSelectedItemsPerPageValue] =
    useState<string>("15");

  const componentRef = useRef<HTMLDivElement>(null);
  // const searchParams = useSearchParams();
  const [customer, setCustomer] = useState<Customer[]>([]);
  // const [isAddOpen, setIsAddOpen] = useState(false);
  // const [isEditOpen, setIsEditOpen] = useState(false);
  // const [isEditCustomer, setIsEditCustomer] = useState(false);
  const pathname = usePathname();
  // const router = useRouter();

  const [isLoading, setIsLoading] = useState(false);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const access_token = Cookies.get("accessToken");
  const { isRefreshed, setIsRefreshed } = useAuth();
  const [department, setDepartment] = useState<department[]>();
  const [search, setSearch] = useState("");

  const startDelayedLoading = () => {
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
    }
    loadingTimeoutRef.current = setTimeout(() => {
      setIsLoading(true);
    }, 200);
  };

  const stopLoading = () => {
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
      loadingTimeoutRef.current = null;
    }
    setIsLoading(false);
  };

  useEffect(() => {
    stopLoading();
  }, [pathname]);

  useEffect(() => {
    const fetchCustomer = async () => {
      try {
        // const access_token = Cookies.get("accessToken");
        const response = await apiClient.get(`/ecustomer`);
        // Backend returns: { success: true, message: "List Customer", data: [...] }
        // The apiClient response interceptor extracts the data property
        setCustomer(response?.data || []);
      } catch (error) {
        console.error("Failed to fetch customers:", error);
        toast.error(error.message || "Failed to fetch staff");
      }
    };

    fetchCustomer();
  }, [access_token, isRefreshed]);

  useEffect(() => {
    const fetchPackages = async () => {
      try {
        const response = await apiClient.get(`/package`);
        // Backend returns: { success: true, message: "List Package", data: [...] }
        // The apiClient response interceptor extracts the data property
        setDepartment(response?.data || []);
      } catch (error) {
        console.error("Failed to fetch Department:", error);
        toast.error(error.message || "Failed to fetch packages");
      }
    };

    fetchPackages();
  }, [access_token, isRefreshed]);

  // useEffect(() => {
  //   const filterFromUrl = searchParams.get("filter") as CustomerFilter;
  //   if (
  //     filterFromUrl &&
  //     ["All", "Active", "Inactive", "Expired"].includes(filterFromUrl)
  //   ) {
  //     setCurrentFilter(filterFromUrl);
  //     setCurrentPage(1);
  //   }
  // }, [searchParams]);

  const itemsPerPage =
    selectedItemsPerPageValue === "all"
      ? customer?.length
      : parseInt(selectedItemsPerPageValue, 10);

  const filteredCustomers = customer?.filter((customer: Customer) => {
    const matchesSearch =
      customer?.username.toLowerCase().includes(search.toLowerCase());


    return matchesSearch;
  });

  const sortedCustomers = [...(filteredCustomers || [])].sort((a, b) => {
    const usernameA = a.username.toLowerCase();
    const usernameB = b.username.toLowerCase();
    if (usernameA < usernameB) {
      return -1;
    }
    if (usernameA > usernameB) {
      return 1;
    }
    return 0;
  });

  const totalPages =
    itemsPerPage === 0 ? 1 : Math.ceil(sortedCustomers.length / itemsPerPage);

  const currentCustomers = sortedCustomers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const scrollToTop = () => {
    if (componentRef.current) {
      // Find the closest parent with overflow-y: auto or scroll
      let currentElement: HTMLElement | null = componentRef.current;
      while (currentElement && currentElement !== document.body) {
        const style = window.getComputedStyle(currentElement);
        // Ensure the element has scrollable content and is visually scrollable
        if (
          (style.overflowY === "auto" || style.overflowY === "scroll") &&
          currentElement.scrollHeight > currentElement.clientHeight
        ) {
          currentElement.scrollTop = 0;
          return; // Found and scrolled the container
        }
        currentElement = currentElement.parentElement;
      }
      // Fallback to window scroll if no specific scrollable parent is found within the component's hierarchy
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  useEffect(() => {
    scrollToTop();
  }, [currentPage]);

  // Handles toggling the "Add Customer" form
  // const handleAddCustomerToggle = () => {
  //   setShowForm(!showForm);
  //   setIsAddOpen(false)
  //   setSelectedCustomer(null); // Ensure no customer is selected when adding
  //   scrollToTop(); // Scroll to top when toggling mode
  // };

  // // Handles selecting a customer for VIEWING details (e.g., clicking username)
  // const handleSelectCustomer = (customer: Customer) => {
  //   startDelayedLoading();
  //   router.push(`/app/staff/view?id=${customer.id}`);
  //   setSelectedCustomer(customer);
  //   setShowForm(false); // Hide any forms when a customer is selected for view
  //   scrollToTop(); // Scroll to top when a customer is selected
  // };

  // // Handles selecting a customer for EDITING (e.g., clicking edit icon)
  // const handleEditCustomer = (customer: Customer) => {
  //   setSelectedCustomer(customer); // Set the customer to be edited
  //   setShowForm(true); // Show the form, which will now be the edit form
  //   scrollToTop();
  // };

  // Handles going back to the customer list from any view/form
  // const handleBackToList = () => {
  //   setSelectedCustomer(null);
  //   setShowForm(false);
  //   scrollToTop(); // Scroll to top when going back to list view
  // };

  const handleFilterClick = (filter: CustomerFilter) => {
    setCurrentFilter(filter);
    setCurrentPage(1);
    setSelectedCustomer(null); // Clear selected customer when applying filter
    setShowForm(false); // Hide any forms when applying filter
  };

  const handleItemsPerPageChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const value = e.target.value;
    setSelectedItemsPerPageValue(value);
    setCurrentPage(1);
  };

  return (
    <div className="space-y-3">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center flex-wrap gap-4">
        <h1 className="text-lg sm:text-lg font-bold sm:mb-3 text-wrap">
          External DB mapped staff
        </h1>

        {!showForm && !selectedCustomer && (
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 w-full sm:w-auto">
            <Input // Using Input component for consistency
              type="text"
              placeholder="Search User"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="w-full sm:w-[250px] p-1 border border-gray-300 rounded-full px-3 focus:ring focus:border-blue-300"
            />
          </div>
        )}
      </div>
      <div
        ref={componentRef}
        className="mx-auto bg-white rounded-lg p-5 sm:p-6 lg:p-5 max-w-full"
      >
        {/* Loading Spinner */}
        {isLoading && (
          <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-[9999]">
            <div className="flex space-x-2">
              <div className="w-3 h-3 bg-white rounded-full animate-bounce-slow"></div>
              <div className="w-3 h-3 bg-white rounded-full animate-bounce-medium"></div>
              <div className="w-3 h-3 bg-white rounded-full animate-bounce-fast"></div>
            </div>
          </div>
        )}

        {/* Conditional Rendering of Forms/Views */}
        {selectedCustomer ? ( // If a customer is selected AND no form is showing, show CustomerView
          <div className="p-2 sm:p-0">
            <CustomerView
              selectedCustomer={selectedCustomer}
              setSelectedCustomer={setSelectedCustomer}
            />
          </div>
        ) : (
          <>
            <div className="w-full mb-4">
              <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mb-4">
                <div className="flex flex-col sm:flex-row items-center gap-4">
                  <div
                    className={`bg-white px-3 py-1 rounded-md  border ${currentFilter === "All"
                      ? "border-blue-300 ring-blue-200"
                      : "border-blue-200 hover:border-blue-300"
                      } text-center cursor-pointer transition-all duration-200`}
                    onClick={() => handleFilterClick("All")}
                  >
                    <h3 className="text-sm font-medium text-blue-700 ">
                      Total Staff:
                      <span className="text-sm font-bold text-blue-800">
                        {customer?.length}
                      </span>
                    </h3>
                  </div>

                  {/* Search Input */}
                </div>

                {/* Right Side: Show entries dropdown */}
                <div className="flex items-center gap-2 text-xs">
                  <span>Show</span>
                  <select
                    value={selectedItemsPerPageValue}
                    onChange={handleItemsPerPageChange}
                    className="border border-gray-300 rounded-md p-1.5 h-7 text-xs focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="15">15</option>
                    <option value="30">30</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                    <option value="all">All</option>
                  </select>
                  <span>entries</span>
                </div>
              </div>
            </div>

            <div className="w-full overflow-x-auto">
              <table className="min-w-max w-full">
                <thead className="bg-gray-200 text-left text-xs uppercase align-middle">
                  <tr>
                    <th className=" px-4 sm:px-4 py-2 hidden md:table-cell">
                      S.N.
                    </th>
                    <th className=" px-4 sm:px-4 py-2">Username</th>
                    <th className=" px-4 sm:px-4 py-2">Department</th>
                    <th className=" px-4 sm:px-4 py-2 text-end">Action</th>
                  </tr>
                </thead>
                <tbody>
                  {currentCustomers?.length === 0 ? (
                    <tr>
                      <td
                        colSpan={7}
                        className="px-1 sm:px-2 py-8 text-center text-blue-900 text-sm"
                      >
                        Oops! No user matched your search
                      </td>
                    </tr>
                  ) : (
                    currentCustomers.map((customer, index) => (
                      <tr
                        key={customer?.id}
                        className={`border-b hover:bg-gray-50 ${selectedCustomer?.id === customer?.id
                          ? "bg-blue-50"
                          : ""
                          }`}
                      >
                        <td className="px-4 sm:px-4 py-1 text-xs hidden md:table-cell">
                          {(currentPage - 1) * itemsPerPage + index + 1}
                        </td>

                        <td
                          className="px-4 sm:px-4 py-1 text-xs text-blue-600  cursor-pointer"
                        // onClick={() => handleSelectCustomer(customer)}
                        >
                          <Link
                            href={`/app/staff/view?id=${customer.id}`}
                            className="font-medium text-blue-500"
                            onClick={(e) => {
                              e.stopPropagation();
                              startDelayedLoading();
                            }}
                          >
                            {customer?.username}
                          </Link>

                        </td>
                        <td className="px-4 sm:px-4 py-1 text-xs">
                          {customer?.package}
                        </td>

                        <td className="px-4 sm:px-4 py-1 text-xs">
                          <div className="flex items-center justify-end gap-1">
                            <DeleteStaffDialog
                              username={customer?.username}
                              id={customer?.id}
                              setIsRefreshed={setIsRefreshed}
                            />


                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            {totalPages > 1 && (
              <div className="flex items-center gap-1 justify-left mt-4">
                <Button
                  className="rounded-full w-8 h-6"
                  size="ss"
                  onClick={() => setCurrentPage((p) => Math.max(p - 1, 1))}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft />
                </Button>
                <span className="text-[12px]">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  className="rounded-full w-8 h-6"
                  size="ss"
                  onClick={() =>
                    setCurrentPage((p) => Math.min(p + 1, totalPages))
                  }
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight />
                </Button>
              </div>
            )}
          </>
        )}
      </div>
      {/* {isAddOpen && (
        
            <ISPCustomerForm
              onFormSuccess={handleBackToList}
              onCancel={() => setIsAddOpen(false)}
            />
          
      )}
      {isEditOpen && (
        
            <CustomerEdit
              customer={isEditCustomer}
              onCancel={() => setIsEditOpen(false)}
            />
      )} */}

    </div>
  );
}

// DeleteStaffDialog component
function DeleteStaffDialog({
  username,
  id,
  setIsRefreshed,
}: {
  username: string;
  id: number;
  setIsRefreshed: React.Dispatch<React.SetStateAction<boolean>>;
}) {
  const [showConfirm, setShowConfirm] = useState(false);
  const [inputName, setInputName] = useState("");
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (inputName !== username) {
      toast.error("Staff name does not match");
      return;
    }

    setIsDeleting(true);
    try {
      await apiClient.delete(`/customer/${id}`);
      // Backend returns: { success: true, message: "Customer has been deleted" }
      toast.success("Staff member deleted successfully");
      setIsRefreshed((prev) => !prev);
      setShowConfirm(false);
    } catch (error) {
      console.error("Failed to delete staff member:", error);
      toast.error(error.message || "Failed to delete staff member");
    } finally {
      setIsDeleting(false);
    }
  };

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        setShowConfirm(false);
        setInputName("");
      }
    };

    if (showConfirm) {
      document.addEventListener("keydown", handleKeyDown);
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [showConfirm]);

  return (
    <>
      <Button
        onClick={() => setShowConfirm(true)}
        className="bg-red-500 hover:bg-red-600 text-white p-2 rounded-md h-7 w-7"
      >
        <Trash2 className="h-4 w-4" />
      </Button>

      {showConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50">
          <div className="bg-white p-7 rounded-lg w-auto max-w-sm shadow-lg">
            <h2 className="text-xl font-bold mb-4 text-center">
              Confirm Deletion
            </h2>
            <p className="mb-2 text-left">
              Are you sure you want to delete the staff member
              <span className="font-semibold">{username}</span>?
            </p>
            <p className="mb-4 text-sm text-gray-600">
              Type the username to confirm:
            </p>
            <input
              className="border px-3 py-2 w-full mb-4 rounded-md"
              value={inputName}
              onChange={(e) => setInputName(e.target.value)}
              placeholder="Enter username"
              autoFocus
            />
            {inputName && inputName !== username && (
              <p className="text-red-500 text-sm mb-4">
                Username does not match
              </p>
            )}
            <div className="flex justify-between gap-2 flex-wrap">
              <button
                onClick={() => {
                  setShowConfirm(false);
                  setInputName("");
                }}
                className="px-4 py-2 bg-gray-300 rounded-md w-full sm:w-auto"
                disabled={isDeleting}
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                disabled={inputName !== username}
                className={`px-4 py-2 rounded-md w-full sm:w-auto 
                ${inputName !== username
                    ? "bg-red-300 cursor-not-allowed"
                    : "bg-red-600 text-white"
                  }`}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
