"use client";

import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import apiClient from "@/lib/apiClient";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";
import jwt from "jsonwebtoken";
import { toast } from "sonner";
import { useAuth } from "@/context/AuthContext";
import { Eye, EyeOff } from "../icons/list";

export interface Packages {
  id: number;
  package_name: string;
}
export type UserPackages = string;

// Define the interface for the props this component expects
interface ISPCustomerFormProps {
  onFormSuccess?: () => void;
  onCancel?: () => void;
}
const accessToken = Cookies.get("accessToken");
const decoded = jwt.decode(accessToken) as any;
const org_id = decoded?.org_id;

export default function CustomerAddForm({
  onFormSuccess,
  onCancel,
}: ISPCustomerFormProps) {
  const [form, setForm] = useState({
    first_name: "",
    last_name: "",
    username: "",
    password: "",
    confirmpassword: "",
    email: "",
    pack_id: "",
    org_id: org_id,
    status: "Active",
  });

  const handleChange = (field: keyof typeof form, value: string) => {
    if (field === "province") {
      setForm({ ...form, province: value, district: "", municipality: "" });
    } else if (field === "district") {
      setForm({ ...form, district: value, municipality: "" });
    } else {
      setForm({ ...form, [field]: value });
    }
  };

  const [packages, setPackages] = useState<Packages[]>([]);
  const [loadingPackages, setLoadingPackages] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { isRefreshed, setIsRefreshed } = useAuth();
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (form.password !== form.confirmpassword) {
      toast.error("Passwords do not match");
      return;
    }

    try {
      const { confirmpassword, ...rest } = form;
      const payload = { ...rest, org_id: org_id };
      await apiClient.post("/customer", payload);
      // Backend returns: { success: true, message: "Customer created", cust_id: ... }
      router.push("/app/staff");
      setIsRefreshed((prev) => !prev);
    } catch (error) {
      console.error("Failed to create user:", error);
      toast.error(error.message || "Failed to create user");
      return;
    }

    setTimeout(() => {
      toast.success("Staff added successfully!");
      if (onFormSuccess) {
        onFormSuccess();
        setIsRefreshed((prev) => !prev);
      }
      setForm({
        pack_id: "",
        first_name: "",
        last_name: "",
        username: "",
        password: "",
        confirmpassword: "",
        email: "",
        org_id: "",
        status: "Active",
      });
    }, 500);
  };

  const handlePasswordChange = (password: string) => {
    setForm((prev) => ({ ...prev, password }));
  };

  const handleConfirmPasswordChange = (confirmpassword: string) => {
    setForm((prev) => ({ ...prev, confirmpassword }));
  };

  const handleCancelClick = () => {
    if (onCancel) {
      onCancel(); // Call the cancel callback if provided
    } else if (onFormSuccess) {
      // Fallback: If onCancel is not provided, use onFormSuccess to close the form
      onFormSuccess();
    }
  };
  useEffect(() => {
    const fetchPackages = async () => {
      try {
        const response = await apiClient.get("/package");
        // Backend returns: { success: true, message: "List Package", data: [...] }
        // The apiClient response interceptor extracts the data property
        setPackages(response?.data || []);
      } catch (error) {
        console.error("Failed to fetch departments:", error);
        toast.error(error.message || "Failed to fetch departments");
      } finally {
        setLoadingPackages(false);
      }
    };

    fetchPackages();
  }, []);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      
      <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm" />
      <div className="relative bg-white rounded-xl shadow-2xl overflow-hidden max-w-md md:max-w-[45rem] w-full max-h-[95vh] flex flex-col">
        
        <div className="px-5 py-6 bg-gradient-to-r bg-primary text-white border-b">
          <h1 className="text-lg font-extrabold text-center">Add New Staff</h1>
        </div>
        <form onSubmit={handleSubmit} className="flex-grow p-10 grid grid-cols-1 gap-x-6 gap-y-4 overflow-y-auto custom-scrollbar space-y-2">
          <div className="text-xs grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="first-name">First Name</Label>
              <Input
                id="first-name"
                type="text"
                value={form.first_name}
                onChange={(e) => handleChange("first_name", e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="last-name">Last Name</Label>
              <Input
                id="last-name"
                type="text"
                value={form.last_name}
                onChange={(e) => handleChange("last_name", e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                type="text"
                value={form.username}
                onChange={(e) => handleChange("username", e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="email-address">Email</Label>
              <Input
                id="email-address"
                type="email"
                value={form.email}
                onChange={(e) => handleChange("email", e.target.value)}
              />
            </div>

            <div className="relative">
              <Label className="text-sm font-medium text-gray-700">
                
                Password
              </Label>
              <Input
                type={showPassword ? "text" : "password"}
                placeholder="Password"
                value={form.password}
                onChange={(e) => handlePasswordChange(e.target.value)}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 -translate-y-1 h-7"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>

            <div className="relative">
              <Label className="text-sm font-medium text-gray-700">
                
                Confirm Password
              </Label>
              <Input
                type={showConfirmPassword ? "text" : "password"}
                placeholder="Confirm Password"
                value={form.confirmpassword}
                onChange={(e) => handleConfirmPasswordChange(e.target.value)}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 -translate-y-1 h-7"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>

            <PackageSelect
              pack_id={form.pack_id}
              setSelectedPackagesId={(id) => handleChange("pack_id", id)}
              packages={packages}
              loading={loadingPackages}
            />

            <div className="space-y-2">
              <Label>Status *</Label>
              <div className="flex gap-4">
                {/* Active Button */}
                <button
                  type="button"
                  aria-pressed={form.status === "Active"}
                  className={`w-20 h-8 border-2 rounded-lg flex items-center justify-center
          ${form.status === "Active"
                      ? "border-blue-600 bg-blue-600 text-white"
                      : "border-gray-300 bg-white text-black"
                    }
          hover:border-blue-400 transition-colors font-medium`}
                  onClick={() => handleChange("status", "Active")}
                >
                  Active
                </button>

                {/* Inactive Button */}
                <button
                  type="button"
                  aria-pressed={form.status === "Inactive"}
                  className={`w-20 h-8 border-2 rounded-lg flex items-center justify-center
          ${form.status === "Inactive"
                      ? "border-blue-600 bg-blue-600 text-white"
                      : "border-gray-300 bg-white text-black"
                    }
          hover:border-blue-400 transition-colors font-medium`}
                  onClick={() => handleChange("status", "Inactive")}
                >
                  Inactive
                </button>
              </div>
            </div>
          </div>

          <div className="flex flex-col justify-end sm:flex-row gap-2 mt-4">
            <Button
              size="sm"
              type="button"
              variant="outline"
              onClick={handleCancelClick}
            >
              
              {/* Added Cancel Button */}
              Cancel
            </Button>
            <Button type="submit" className="w-full sm:w-auto" size="sm">
              Add Staff
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}

function PackageSelect({
  pack_id,
  setSelectedPackagesId,
  packages,
  loading,
}: {
  pack_id: string;
  setSelectedPackagesId: (id: string) => void;
  packages: Packages[];
  loading: boolean;
}) {
  return (
    <div className="space-y-1">
      <label className="text-sm font-medium text-gray-700">Department</label>
      <select
        className="w-full p-2 border rounded-md"
        value={pack_id}
        onChange={(e) => setSelectedPackagesId(e.target.value)}
        required
      >
        <option value="">Select an Department</option>
        {packages?.map((packages) => (
          <option key={packages?.id} value={packages?.id.toString()}>
            {packages?.package_name}
          </option>
        ))}
      </select>
    </div>
  );
}
