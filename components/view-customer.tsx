"use client"; // This must be at the very top of the file

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import CustomerEdit from "@/components/customer/customer-edit";
import { Button } from "@/components/ui/button";
import { ListRestart, SquarePen } from "@/components/icons/list";
import CustomerBasicInfo from "@/components/customer/CustomerBasicInfo";
import CustomerRecentUsage from "@/components/customer/CustomerRecentUsage";
import CustomerHeader from "@/components/customer/CustomerHeader";
import CustomerUsageSection from "@/components/customer/CustomerUsageSection";
import CustomerGraphSection from "@/components/customer/CustomerGraphSection";
import apiClient from "@/lib/apiClient";
import { GraphRange, RadAcctSession } from "@/types/interface-type";
import { Customer } from "./customer/customer-details";

const TABS = ["Staff Details", "Usage", "Graph"] as const;

type Props = {
  selectedCustomer: Customer | null;
  setSelectedCustomer: (c: any | null) => void;
};

// Helper function to get default dates (can be defined outside or inside the component)
const getDefaultDates = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const day = String(today.getDate()).padStart(2, "0");
  const defaultTodayDate = `${year}-${month}-${day}`;

  const oneMonthAgo = new Date();
  oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

  const prevYear = oneMonthAgo.getFullYear();
  const prevMonth = String(oneMonthAgo.getMonth() + 1).padStart(2, "0");
  const defaultMonthStartDate = `${prevYear}-${prevMonth}-01`;
  return { defaultMonthStartDate, defaultTodayDate };
};

// Ensure your functional component is correctly defined and exported as default
export default function CustomerView({
  selectedCustomer,
  setSelectedCustomer,
}: Props) {
  // ... (rest of your component code)
  const router = useRouter();

  const [showPassword, setShowPassword] = useState(false);
  const [showForm, setShowForm] = useState(false);

  const initialDefaultDates = getDefaultDates();

  const [displayedUsageData, setDisplayedUsageData] = useState<
    RadAcctSession[]
  >([]);
  const [allSessionData, setAllSessionData] = useState<RadAcctSession[] | null>(
    null
  );
  const [activeTab, setActiveTab] = useState<(typeof TABS)[number]>(TABS[0]);

  const [pickerStartDate, setPickerStartDate] = useState<string>(
    initialDefaultDates.defaultMonthStartDate
  );
  const [pickerEndDate, setPickerEndDate] = useState<string>(
    initialDefaultDates.defaultTodayDate
  );

  const [appliedStartDate, setAppliedStartDate] = useState<string>(
    initialDefaultDates.defaultMonthStartDate
  );
  const [appliedEndDate, setAppliedEndDate] = useState<string>(
    initialDefaultDates.defaultTodayDate
  );

  const [selectedGraphRange, setSelectedGraphRange] = useState<string>("1day");
  const [graphRanges, setGraphRanges] = useState<GraphRange[]>([]);
  const [isLoadingUsage, setIsLoadingUsage] = useState<boolean>(false);

  const fetchSessionData = useCallback(async () => {
    if (!selectedCustomer?.username) {
      setAllSessionData([]);
      setGraphRanges([]);
      setDisplayedUsageData([]);
      return;
    }

    setIsLoadingUsage(true);
    try {
      const response = await apiClient.get(
        `customer/stats/${selectedCustomer.username}`
      );
      // Backend returns: { success: true, message: "Customer Status", data: {...} }
      // The apiClient response interceptor extracts the data property
      const sessions = response?.data || [];
      setAllSessionData(sessions);

      const graphData: GraphRange[] = [];
      for (const session of sessions) {
        const { acctinputoctets, acctoutputoctets, acctstarttime } = session;
        graphData.push({
          timeLabel: new Date(acctstarttime).toLocaleTimeString("en-US", {
            hour: "2-digit",
            minute: "2-digit",
            second: "2-digit",
            hour12: false,
          }),
          timestamp: acctstarttime,
          upload: +(acctinputoctets / 1048576).toFixed(2),
          download: +(acctoutputoctets / 1048576).toFixed(2),
        });
      }
      setGraphRanges(graphData);
    } catch (err) {
      console.error("Error fetching session data:", err);
      toast.error(err.message || "Failed to fetch customer statistics");
      setAllSessionData([]);
      setGraphRanges([]);
    } finally {
      setIsLoadingUsage(false);
    }
  }, [selectedCustomer?.username]);

  const applyDateFilter = useCallback(() => {
    if (!allSessionData) {
      setDisplayedUsageData([]);
      return;
    }

    const filterStartDateObj = appliedStartDate
      ? new Date(appliedStartDate + "T00:00:00")
      : null;
    const filterEndDateObj = appliedEndDate
      ? new Date(appliedEndDate + "T23:59:59")
      : null;

    const newFilteredData = allSessionData.filter((record) => {
      if (!record.acctstarttime) {
        return false;
      }

      const recordStartTimestamp = new Date(record.acctstarttime).getTime();

      let passesFilter = true;

      if (
        filterStartDateObj &&
        recordStartTimestamp < filterStartDateObj.getTime()
      ) {
        passesFilter = false;
      }

      if (
        filterEndDateObj &&
        recordStartTimestamp > filterEndDateObj.getTime()
      ) {
        passesFilter = false;
      }

      return passesFilter;
    });
    setDisplayedUsageData(newFilteredData);
  }, [allSessionData, appliedStartDate, appliedEndDate]);

  useEffect(() => {
    if (selectedCustomer?.username) {
      const { defaultMonthStartDate, defaultTodayDate } = getDefaultDates();

      setPickerStartDate(defaultMonthStartDate);
      setPickerEndDate(defaultTodayDate);
      setAppliedStartDate(defaultMonthStartDate);
      setAppliedEndDate(defaultTodayDate);
      fetchSessionData();
    } else {
      setAllSessionData([]);
      setGraphRanges([]);
      setDisplayedUsageData([]);
    }
  }, [selectedCustomer?.username, fetchSessionData]);

  useEffect(() => {
    applyDateFilter();
  }, [allSessionData, appliedStartDate, appliedEndDate, applyDateFilter]);

  const handleDateSubmit = (startDate: string, endDate: string) => {
    setAppliedStartDate(startDate);
    setAppliedEndDate(endDate);
    console.log("Applying usage data filter for:", startDate, "to", endDate);
  };

  const handleDateReset = useCallback(() => {
    const { defaultMonthStartDate, defaultTodayDate } = getDefaultDates();

    setPickerStartDate(defaultMonthStartDate);
    setPickerEndDate(defaultTodayDate);
    setAppliedStartDate(defaultMonthStartDate);
    setAppliedEndDate(defaultTodayDate);
    console.log("Resetting usage data filter to current default dates.");
  }, []);

  const lastThreeUsageRecords = [...(allSessionData || [])]
    .filter((record) => record.username === selectedCustomer?.username)
    .sort(
      (a, b) =>
        new Date(b.acctstarttime).getTime() -
        new Date(a.acctstarttime).getTime()
    )
    .slice(0, 3);

  const handleEditCustomer = (customer: any) => {
    setSelectedCustomer(customer);
    setShowForm(true);
  };

  return (
    <div className="mb-1 space-y-4 ">
      {/* ... (rest of your JSX code) ... */}
      {/* <CustomerHeader
        firstName={selectedCustomer?.first_name}
        lastName={selectedCustomer?.last_name}
        status={selectedCustomer?.status}
        address={selectedCustomer?.address}
      /> */}

      <div className="flex flex-wrap justify-between text-center bg-gray-50 border-b rounded-lg border-gray-300">
        {TABS.map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`flex-1 min-w-[140px] py-2 text-sm ${
              activeTab === tab
                ? "border-b-2 border-blue-600 text-blue-600 font-medium"
                : "text-gray-700 hover:text-gray-900"
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      <div>
        {activeTab === "Staff Details" && (
          <div className="grid grid-cols-1 gap-4">
            <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
              <CustomerBasicInfo
                customer={selectedCustomer}
                showPassword={showPassword}
                onTogglePassword={() => setShowPassword(!showPassword)}
              />
            </div>
            <CustomerRecentUsage usageRecords={lastThreeUsageRecords} />
          </div>
        )}

        {activeTab === "Usage" && (
          <CustomerUsageSection
            initialStartDate={pickerStartDate}
            initialEndDate={pickerEndDate}
            setInitialStartDate={setPickerStartDate}
            setInitialEndDate={setPickerEndDate}
            handleDateSubmit={handleDateSubmit}
            handleDateReset={handleDateReset}
            filteredUsageByDate={displayedUsageData}
            isLoading={isLoadingUsage}
          />
        )}

        {activeTab === "Graph" && (
          <CustomerGraphSection
            selectedGraphRange={selectedGraphRange}
            setSelectedGraphRange={setSelectedGraphRange}
            graphRanges={graphRanges}
          />
        )}
      </div>

      <div className="flex flex-col sm:flex-row gap-2 mt-4">
        <Button
          onClick={() => router.push("/app/staff")}
          className="w-full sm:w-auto px-3 rounded-full"
        >
          <ListRestart /> Back to List
        </Button>
        <Button
          variant="outline"
          onClick={() => handleEditCustomer(selectedCustomer)}
          className="w-full sm:w-auto px-3 rounded-full"
        >
          <SquarePen />
          Edit Staff
        </Button>
      </div>

      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-40 p-4">
          <div className="bg-white rounded-lg p-6 max-h-[90vh] overflow-y-auto w-full max-w-2xl relative">
            <CustomerEdit
              customer={selectedCustomer}
              onCancel={() => setShowForm(false)}
              onSave={() => {
                setShowForm(false);
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
}
