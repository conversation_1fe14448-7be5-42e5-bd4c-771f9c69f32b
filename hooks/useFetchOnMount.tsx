"use client";
import { useEffect, useState } from "react";
import apiClient from "@/lib/apiClient";
import { useAuth } from "@/context/AuthContext";

interface SessionData {
  acctinputoctets: number;
  acctoutputoctets: number;
  acctstarttime: string;
  // Add other fields as needed
}

export function useFetch(url_parameter: string | undefined) {
  const [data, setData] = useState<SessionData[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<any>(null);
  const { isRefreshed } = useAuth();

  useEffect(() => {
    if (!url_parameter) return;

    const fetchStats = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await apiClient.get(url_parameter);
        // Backend returns: { success: true, message: "...", data: [...] }
        // The apiClient response interceptor extracts the data property
        const sessions = response.data || [];
        setData(sessions);
      } catch (err) {
        console.error("Failed to fetch data:", err);
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [url_parameter, isRefreshed]);

  return { data, loading, error };
}
