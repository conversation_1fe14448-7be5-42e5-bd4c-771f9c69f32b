{"name": "radius-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build --no-lint", "start": "NODE_ENV=production node server.js", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@next-auth/prisma-adapter": "^1.0.7", "@radix-ui/react-accordion": "latest", "@radix-ui/react-alert-dialog": "latest", "@radix-ui/react-aspect-ratio": "latest", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "latest", "@radix-ui/react-context-menu": "latest", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "latest", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "latest", "@radix-ui/react-navigation-menu": "latest", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "latest", "@radix-ui/react-scroll-area": "latest", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "latest", "@radix-ui/react-toggle-group": "latest", "@radix-ui/react-tooltip": "latest", "@types/pg": "^8.15.2", "autoprefixer": "^10.4.20", "axios": "^1.9.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "latest", "cookie-parser": "^1.4.7", "cookie-signature": "^1.2.2", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "embla-carousel-react": "latest", "express": "^5.1.0", "input-otp": "latest", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.454.0", "mini-css-extract-plugin": "^2.9.2", "mongoose": "^8.15.1", "navigation": "^6.3.0", "next": "15.2.4", "next-auth": "^4.24.11", "next-themes": "latest", "nodemon": "^3.1.10", "pg": "^8.16.0", "react": "^19", "react-datepicker": "^8.4.0", "react-day-picker": "latest", "react-dom": "^19", "react-hook-form": "latest", "react-resizable-panels": "latest", "recharts": "^2.15.4", "sonner": "^2.0.5", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "latest", "zod": "^3.24.1"}, "devDependencies": {"@types/axios": "^0.9.36", "@types/cookie-signature": "^1.1.2", "@types/node": "^22", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}