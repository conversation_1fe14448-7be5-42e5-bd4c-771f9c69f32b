FROM node:20-alpine

# Create app directory
WORKDIR /app
COPY package*.json ./

# Install app dependencies
RUN npm install

COPY . .

ARG NEXT_PUBLIC_API_URL

RUN npm run build

RUN chown -R node:node /app
USER node
# Copy source code

# COPY --chown=node:node package*.json ./

# # Install dependencies as root
# RUN npm install

# # Copy rest of the source code (with correct ownership)
# COPY --chown=node:node . .
# RUN chmod -R u+rwX /app
# USER node

# Optional: install global tools
# RUN npm install -g ts-node typescript

# Expose port (if needed)
EXPOSE 3000

# Default command (for dev)
#  CMD ["npm", "run", "dev"]
CMD ["npm", "run","start"]
