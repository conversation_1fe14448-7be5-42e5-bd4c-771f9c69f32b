import "./globals.css";
import type React from "react";
import type { Metadata } from "next";
import { Inter, <PERSON>o } from "next/font/google";
import { AuthProvider } from "@/context/AuthContext";
import { Toaster } from "sonner";
import ErrorBoundary from "@/components/ErrorBoundary";

const roboto = Roboto({ subsets: ["latin"] });

export const commonMetadata: Metadata = {
  // title: {
  //   default: "Nepal Police",
  //   template: "%s | Nepal Police",
  // },
  description: "staff management dashboard",
  icons: {
    icon: "/images/nepal_police_small.png",
  },
};

interface RootLayoutProps {
  children: React.ReactNode;
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={roboto.className} suppressHydrationWarning>
        <AuthProvider children={undefined}>
          {children}
          <Toaster position="top-center" richColors />
        </AuthProvider>
      </body>
    </html>
  );
}
