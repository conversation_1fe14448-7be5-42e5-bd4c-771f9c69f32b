import RootLayout, { commonMetadata } from "../root-layout";
import Template from "@/components/template";
import { RouteAuthenticator } from "@/components/Auth/RouteAuth";
import type React from "react";
import type { Metadata } from "next";

export const metadata: Metadata = {
  ...commonMetadata,
  title: {
    default: "Nepal Police",
    template: "%s | Nepal Police",
  },
};

export default function AppRootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <RootLayout
      children={
        <RouteAuthenticator children={<Template children={children} />} />
      }
    />
  );
}
