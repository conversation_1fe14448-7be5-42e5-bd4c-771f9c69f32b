"use client";

import { useState } from "react";

import GuestVoucherAccessPage from "@/components/guest/voucher-access";
import GuestSponsorAccessPage from "@/components/guest/sponsor-access";

export default function GuestAccessManagementPage() {
  const [activePage, setActivePage] = useState<"guest" | "sponsor">("guest");

  return (
    <div className="p-5 mx-auto">
      <h1 className="text-lg sm:text-lg font-bold mb-3 sm:mb-3 text-wrap">
        Guest Access Management
      </h1>
      <div className="bg-white rounded-lg shadow-sm">
        <div className="p-3 sm:p-5 border-b">
          <div className="flex flex-col sm:flex-row gap-4 w-full ">
            <button
              onClick={() => setActivePage("guest")}
              className={`px-4 py-2 rounded-full border w-full sm:w-auto h-10 ${
                activePage === "guest"
                  ? "bg-blue-600 text-white"
                  : "border-gray-300 text-gray-700 bg-white"
              }`}
            >
              Voucher Access
            </button>
            <button
              onClick={() => setActivePage("sponsor")}
              className={`px-4 py-2 rounded-full border w-full sm:w-auto h-10 ${
                activePage === "sponsor"
                  ? "bg-blue-600 text-white"
                  : "border-gray-300 text-gray-700 bg-white"
              }`}
            >
              Sponsor Access
            </button>
          </div>
        </div>

        <div className="p-3 sm:p-5">
          {activePage === "guest" ? (
            <GuestVoucherAccessPage />
          ) : (
            <GuestSponsorAccessPage />
          )}
        </div>
      </div>
    </div>
  );
}
