"use client";

import React, { useState, useEffect } from "react";
import {
  Plus,
  SquarePen,
  Trash2,
  ChevronLeft,
  ChevronRight,
} from "@/components/icons/list";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input"; // Assuming these are valid paths
import apiClient from "@/lib/apiClient";
import { PackageInfo } from "@/types/interface-type";
import { toast } from "sonner";
import DeleteConfirm from "@/components/delete-dailog";
import ISPpackagesForm from "@/components/department/department-form";
import { useFetch } from "@/hooks/useFetchOnMount";
import { useAuth } from "@/context/AuthContext";

export default function PackagesPage() {
  const [search, setSearch] = useState("");
  const [showDeleteDailog, setDeletetDailog] = useState(false);
  const [isAddOpen, setIsAddOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [selectedDep, setSelectedDep] = useState<PackageInfo>();
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedItemsPerPageValue, setSelectedItemsPerPageValue] =
    useState<string>("15");
  const { data: pack, loading: packLoading } = useFetch("/package");
  const packagesList: PackageInfo[] = pack || [];
  const { setIsRefreshed } = useAuth(); 

  const filteredPackages = packagesList
    ? [...packagesList]
        .filter((pkg) =>
          pkg?.package_name?.toLowerCase().includes(search.toLowerCase())
        )
        .sort(
          (a, b) =>
            a?.package_name?.localeCompare(b?.package_name || "", undefined, {
              sensitivity: "base",
            }) || 0
        )
        
    : [];

  // Pagination logic
  const itemsPerPage =
    selectedItemsPerPageValue === "all"
      ? filteredPackages.length
      : parseInt(selectedItemsPerPageValue, 10);

  const totalPages =
    itemsPerPage === 0 ? 1 : Math.ceil(filteredPackages.length / itemsPerPage);

  const currentPackages = filteredPackages.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Handle items per page change
  const handleItemsPerPageChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const value = e.target.value;
    setSelectedItemsPerPageValue(value);
    setCurrentPage(1);
  };

  // Reset to page 1 when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [search]);

  const handleSubmit = async (packageData: any) => {
    try {
      const response = await apiClient.post("/package", packageData);
      // Backend returns: { success: true, message: "Created Package" }
      toast.success("Department created sucessfully");
      setIsAddOpen(false);
      setIsRefreshed((prev: boolean) => !prev);
      return true;
    } catch (error) {
      console.error("Failed to create department:", error);
      toast.error(error.message || "Failed to create department");
      return false;
    }
  };

  const handleEditPackage = (pkg: PackageInfo) => {
    setSelectedDep(pkg);
    setIsEditOpen(true);
  };
  const handleEditSubmit = async (updatedPackage: any) => {
    const { id, ...payload } = updatedPackage;
    try {
      const response = await apiClient.patch(`/package/${id}`, payload);
      // Backend returns: { success: true, message: "Package has been updated" }
      toast.success("Department has been updated");
      setIsEditOpen(false);
      setIsRefreshed((prev: boolean) => !prev);
      return true;
    } catch (err: any) {
      console.error("Failed to edit department:", err);
      toast.error(err.message || "Failed to edit the department information");
      return false;
    }
  };
  const handleDeleteClick = (pkg: PackageInfo) => {
    setSelectedDep(pkg);

    setDeletetDailog(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await apiClient.delete(`/package/${id}`);
      // Backend returns: { success: true, message: "Package has been deleted" }
      toast.success("Department deleted successfully!");
      console.log("Package deleted successfully");
      setDeletetDailog(false);
      setIsRefreshed((prev) => !prev);
    } catch (err) {
      console.error("Failed to delete package:", err);
      toast.error(err.message || "Failed to delete department");
    }
  };

  return (
    <div className="p-5 sm:p-5 space-y-3">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center flex-wrap gap-4">
        <h1 className="text-lg sm:text-lg font-bold sm:mb-3 text-wrap">
          Department Management
        </h1>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 w-full sm:w-auto ">
          <Input
            type="text"
            placeholder="Search Departments by Name"
            value={search}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setSearch(e.target.value)
            }
            className="w-full sm:w-[250px] p-1 border border-gray-300  px-3 focus:ring focus:border-blue-300 rounded-full"
          />
          <Button
            onClick={() => setIsAddOpen(true)}
            className="w-full bg-buttoncolor sm:w-auto rounded-full"
          >
            <Plus />
            Add Department
          </Button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-4 overflow-x-auto">
        <div className="w-full overflow-x-auto ">
          <table className="min-w-max w-full">
            <thead className="bg-gray-200 text-left text-xs uppercase align-middle">
              <tr>
                <th className="px-4 py-2 ">S.N.</th>
                <th className="px-4 py-2 ">Department</th>
                <th className="px-4 py-2 ">Status</th>
                <th className="px-4 py-2 ">VLAN</th>
                <th className="px-4 py-2 text-end">Actions</th>
              </tr>
            </thead>
            <tbody>
              {packLoading ? (
                <tr>
                  <td colSpan={7} className="text-center py-8 text-gray-500">
                    Loading departments...
                  </td>
                </tr>
              ) : filteredPackages?.length === 0 ? (
                <tr>
                  <td
                    colSpan={7}
                    className="px-1 sm:px-2 py-8 text-center text-blue-900 text-sm"
                  >
                    Oops! No departments matched your search
                  </td>
                </tr>
              ) : (
                currentPackages?.map((pkg, index) => (
                  <tr key={pkg?.id} className="border-b hover:bg-gray-50">
                    <td className="px-4 py-1 text-xs">
                      {(currentPage - 1) * itemsPerPage + index + 1}
                    </td>
                    <td className="px-4 py-1 text-xs">{pkg?.package_name}</td>
                    <td className="px-4 py-1 text-xs">{pkg?.status}</td>
                    <td className="px-4 py-1 text-xs">{pkg?.vlan}</td>
                    <td className="px-4 py-1 text-xs text-end">
                      
                      {/* Align actions to end */}
                      <div className="flex items-center justify-end gap-1">
                        
                        {/* Use justify-end for actions */}
                        <Button
                          onClick={() => handleEditPackage(pkg)}
                          className="bg-green-500 hover:bg-green-600 text-white p-1 rounded-md h-7 w-7"
                        >
                          <SquarePen className="h-4 w-4" />
                        </Button>
                        <Button
                          onClick={() => handleDeleteClick(pkg)}
                          className="bg-red-500 hover:bg-red-600 text-white p-1 rounded-md h-7 w-7"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>

          {/* Pagination controls */}
          {totalPages > 1 && (
            <div className="flex items-center gap-1 justify-end mt-4">
              
              {/* Changed justify-left to justify-end for pagination */}
              <select
                value={selectedItemsPerPageValue}
                onChange={handleItemsPerPageChange}
                className="border rounded-md p-1 text-xs"
              >
                <option value="15">15 per page</option>
                <option value="30">30 per page</option>
                <option value="50">50 per page</option>
                <option value="all">All</option>
              </select>
              <Button
                className="rounded-full w-8 h-6"
                size="ss"
                onClick={() =>
                  setCurrentPage((p: number) => Math.max(p - 1, 1))
                }
                disabled={currentPage === 1}
              >
                <ChevronLeft />
              </Button>
              <span className="text-[12px]">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                className="rounded-full w-8 h-6"
                size="ss"
                onClick={() =>
                  setCurrentPage((p: number) => Math.min(p + 1, totalPages))
                }
                disabled={currentPage === totalPages}
              >
                <ChevronRight />
              </Button>
            </div>
          )}
        </div>
      </div>
      {showDeleteDailog && selectedDep && (
        <DeleteConfirm
          id={selectedDep?.id}
          paraValue="Department"
          value={selectedDep?.package_name}
          onClose={() => setDeletetDailog(false)}
          onDelete={handleDelete}
          loading={false}
        ></DeleteConfirm>
      )}
      {/* These should be outside the table div and not in a flex container like this */}
      {isAddOpen && (
        <ISPpackagesForm
          onSubmit={handleSubmit}
          onCancel={() => setIsAddOpen(false)}
        />
      )}
      {isEditOpen && (
        <ISPpackagesForm
          onSubmit={handleEditSubmit}
          onCancel={() => setIsEditOpen(false)}
          initialData={selectedDep}
          isEdit={true}
        />
      )}
    </div>
  );
}
