"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import apiClient from "@/lib/apiClient";
import { Users, UserCog, Monitor } from 'lucide-react';


export default function SettingsPage() {
  interface User {
    id: string;
    username: string;
    email: string;
    group: string;
    status: string;
    organization: string;
    group_id?: string;
    org_id?: string;
  }

  interface Group {
    id: number;
    name: string;
  }

  interface NAS {
    id: string;
    nasname: string;
    secret: string;
    description: string;
    vendor: string;
  }

  const router = useRouter();
  const [data, setData] = useState({
    users: [] as User[],
    groups: [] as Group[],
    nas: [] as NAS[],
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const totalGroups = data?.groups?.length;
  const totalNas = data?.nas?.length;
  const activeUsers = data?.users?.filter(
    (user) => user?.status?.toLowerCase() === "active"
  ).length;
  const inactiveUsers = data?.users?.filter(
    (user) => user?.status?.toLowerCase() === "inactive"
  ).length;

  const fetchAllData = async () => {
    setLoading(true);
    setError(null);

    try {
      const [usersResponse, groupsResponse, nasResponse] =
        await Promise.allSettled([
          apiClient.get("/user"),
          apiClient.get("/groups"),
          apiClient.get("/nas"),
        ]);

      const newData = {
        users: [] as User[],
        groups: [] as Group[],
        nas: [] as NAS[],
      };

      if (usersResponse.status === "fulfilled") {
        newData.users = usersResponse?.value?.data || [];
      } else {
        console.error("Failed to fetch users:", usersResponse.reason);
      }

      if (groupsResponse.status === "fulfilled") {
        newData.groups =
          groupsResponse?.value?.data?.data ||
          groupsResponse?.value?.data ||
          [];
      } else {
        console.error("Failed to fetch groups:", groupsResponse.reason);
      }

      if (nasResponse.status === "fulfilled") {
        newData.nas =
          nasResponse?.value?.data?.data || nasResponse?.value?.data || [];
      } else {
        console.error("Failed to fetch NAS:", nasResponse.reason);
      }

      setData(newData);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch data";
      setError(errorMessage);
      console.error("Failed to fetch settings data:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAllData();
  }, []);

  if (loading) {
    return (
      <div className="p-5 mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 mb-3 justify-center animate-pulse">
          {[...Array(4)].map((_, index) => (
            <div
              key={index}
              className="bg-white p-4 rounded shadow flex items-center justify-center gap-3"
            >
              <div>
                <div className="h-6 bg-gray-200 rounded w-16 mb-3"></div>
                <div className="h-4 bg-gray-200 rounded w-20"></div>
              </div>
            </div>
          ))}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="bg-white p-4 rounded shadow">
              <div className="h-6 bg-gray-200 rounded w-24 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-12"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-5 mx-auto">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <div className="text-red-600 text-lg font-semibold mb-3">
            Error Loading Settings
          </div>
          <div className="text-red-500 mb-4">{error}</div>
          <button
            onClick={() => fetchAllData()}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-5 mx-auto space-y-3">
      <div className="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-3 gap-3">
        <div className="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">Groups</h3>
          <div className="text-2xl font-bold text-green-600">{totalGroups}</div>
          <div className="text-sm text-gray-500">Total Groups</div>
          {totalGroups === 0 && (
            <div className="text-xs text-red-500 mt-1">
              No groups data loaded
            </div>
          )}
        </div>

        <div className="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">
            Total Devices
          </h3>
          <div className="text-2xl font-bold text-purple-600">{totalNas}</div>
          <div className="text-sm text-gray-500">Total Devices</div>
          {totalNas === 0 && (
            <div className="text-xs text-red-500 mt-1">No NAS data loaded</div>
          )}
        </div>

        <div className="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">
            User Status
          </h3>
          <div className="space-y-1">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Active:</span>
              <span className="text-sm font-semibold text-green-600">
                {activeUsers}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Inactive:</span>
              <span className="text-sm font-semibold text-red-600">
                {inactiveUsers}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold text-gray-700 mb-4">
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-3 gap-3">
          {/* Manage Users */}
          <div
            onClick={() => router.push("/app/settings/users")}
            className="cursor-pointer p-4 border border-gray-200 rounded-lg hover:bg-blue-50 transition-colors flex items-start gap-4"
          >
            <div className="bg-blue-100 p-2 rounded-full text-blue-600">
              <Users className="w-5 h-5" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-700">Manage Users</div>
              <div className="text-xs text-gray-500">Add, edit, or remove users</div>
            </div>
          </div>
          {/* <button
            onClick={() => router.push("/app/settings/users")}
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
          >
            <div className="text-sm font-medium text-gray-700">
              Manage Users
            </div>
            <div className="text-xs text-gray-500">
              Add, edit, or remove users
            </div>
          </button> */}

          {/* Manage Groups */}
          <div
            onClick={() => router.push("/app/settings/groups")}
            className="cursor-pointer p-4 border border-gray-200 rounded-lg hover:bg-blue-50 transition-colors flex items-start gap-4"
          >
            <div className="bg-blue-100 p-2 rounded-full text-blue-600">
              <UserCog className="w-5 h-5" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-700">Manage Groups</div>
              <div className="text-xs text-gray-500">Create and edit groups</div>
            </div>
          </div>

          {/* <button
            onClick={() => router.push("/app/settings/groups")}
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
          >
            
            <div className="text-sm font-medium text-gray-700">
              Manage Groups
            </div>
            <div className="text-xs text-gray-500">Create and edit groups</div>
          </button> */}


          {/* Manage Devices */}
          <div
            onClick={() => router.push("/app/settings/devices")}
            className="cursor-pointer p-4 border border-gray-200 rounded-lg hover:bg-blue-50 transition-colors flex items-start gap-4"
          >
            <div className="bg-blue-100 p-2 rounded-full text-blue-600">
              <Monitor className="w-5 h-5" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-700">Manage Devices</div>
              <div className="text-xs text-gray-500">Configure devices</div>
            </div>
          </div>


          {/* <button
            onClick={() => router.push("/app/settings/devices")}
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
          >
            <div className="text-sm font-medium text-gray-700">
              Manage Devices
            </div>
            <div className="text-xs text-gray-500">Configure devices</div>
          </button> */}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-700 mb-4">
            Recent Users
          </h3>
          {data.users.length > 0 ? (
            <div className="space-y-3">
              {data.users.slice(0, 5).map((user) => (
                <div
                  key={user.id}
                  className="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
                >
                  <div>
                    <div className="font-medium text-gray-900">
                      {user.username}
                    </div>
                    <div className="text-sm text-gray-500">{user.email}</div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-700">
                      {user.group}
                    </div>
                    <div
                      className={`text-xs px-2 py-1 rounded-lg ${user.status?.toLowerCase() === "active"
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                        }`}
                    >
                      {user.status}
                    </div>
                  </div>
                </div>
              ))}
              {data.users.length > 5 && (
                <button
                  onClick={() => router.push("/app/settings/users")}
                  className="w-full text-center text-blue-600 hover:text-blue-800 text-sm font-medium py-2"
                >
                  View all users →
                </button>
              )}
            </div>
          ) : (
            <div className="text-gray-500 text-center py-4">No users found</div>
          )}
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-700 mb-4">
            System Overview
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Groups Available</span>
              <span className="font-semibold text-gray-900">{totalGroups}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Devices</span>
              <span className="font-semibold text-gray-900">{totalNas}</span>
            </div>
            <div className="border-t pt-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Active Users</span>
                <span className="font-semibold text-green-600">
                  {activeUsers}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Inactive Users</span>
                <span className="font-semibold text-red-600">
                  {inactiveUsers}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
