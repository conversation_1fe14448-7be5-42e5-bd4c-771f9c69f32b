"use client";

import { useState } from "react";
import ExternalCustomerUnmap from "@/components/customer/external-customer-unmap";
import ExternalCustomerMap from "@/components/customer/external-customer-map";

export default function ExternalCustomersPage() {
  const [activePage, setActivePage] = useState<"unmap" | "map">("unmap");


  return (
    <div className="p-5 mx-auto">
      <h1 className="text-lg sm:text-lg font-bold mb-3 sm:mb-3 text-wrap">
        External Staff Management
      </h1>
      <div className="bg-white rounded-lg shadow-sm">
        <div className="p-3 sm:p-5 border-b">
          <div className="flex flex-col sm:flex-row gap-4 w-full ">
            <button
              onClick={() => setActivePage("unmap")}
              className={`px-4 py-2 rounded-full border w-full sm:w-auto h-10 ${
                activePage === "unmap"
                  ? "bg-blue-600 text-white"
                  : "border-gray-300 text-gray-700 bg-white"
              }`}
            >
              Unmapped Staff
            </button>
            <button
              onClick={() => setActivePage("map")}
              className={`px-4 py-2 rounded-full border w-full sm:w-auto h-10 ${
                activePage === "map"
                  ? "bg-blue-600 text-white"
                  : "border-gray-300 text-gray-700 bg-white"
              }`}
            >
              Mapped Staff
            </button>
          </div>
        </div>

        <div className="p-3 sm:p-5">
          {activePage === "unmap" ? (
            <ExternalCustomerUnmap />
          ) : (
            <ExternalCustomerMap />
          )}
        </div>
      </div>
    </div>
  );


}
